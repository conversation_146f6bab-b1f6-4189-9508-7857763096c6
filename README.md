# KMP Jimmer 全栈脚手架

> 基于 Kotlin Multiplatform + Jimmer 的现代化全栈开发脚手架，通过 KSP 实现前后端代码生成的完美一致性

## 🚀 技术栈
[![技术栈](https://skillicons.dev/icons?i=kotlin,gradle,idea,wasm,spring,postgres,docker,androidstudio)](https://skillicons.dev)

## 📸 项目展示
![项目截图](images/img_2.png)
![项目截图](images/img_1.png)
![项目截图](images/img.png)

## ✨ 核心特性

### 🎯 **一致性保证** - 单一数据源，多端同步
- **Jimmer 实体生成** (Backend) - 数据模型定义的唯一真相源
- **默认 Controller 生成** (Backend) - 标准化 CRUD 接口
- **网络 API 自动生成** - 解析 Controller 符号，自动生成类型安全的网络调用
- **同构体生成** (跨平台 Shared) - `SysUserIso` 等数据传输对象
- **字典/枚举生成** (跨平台 Shared) - `com.addzero.kmp.generated.enums`
- **矢量图标管理** - `IconKeys` 常量 + `IconMap` 映射
- **JDBC 元数据** (跨平台 Shared) - `com.addzero.kmp.jdbc.meta.jdbcMetadata`

### 🎨 **智能表单生成** - KSP 驱动的动态 UI
- **✅ 基于 Jimmer 实体的动态表单生成** - 包含完整校验逻辑
- **🔄 策略模式架构** - 可扩展的字段类型支持
- **🎯 智能字段识别** - 根据字段名称和类型自动选择合适的输入组件
- **📱 多样化输入组件** - 整数、小数、金额、百分比、日期、邮箱、手机号等
- **💰 智能货币图标** - 根据货币类型自动显示对应图标（¥/$/€等）
- **🔍 RegexEnum 验证** - 统一的正则表达式验证体系
- **🏷️ @Label 注解支持** - 优先使用注解标签，回退到文档注释

### 🧭 **路由导航系统**
- **路由表生成** (跨平台 ComposeApp) - `RouteTable`
- **路由常量** (跨平台 ComposeApp) - `RouteKeys`



## 只需加注解就可以渲染到侧边栏(后续RBAC叠加ksp元数据控制权限)
![项目截图](images/img_3.png)

```kotlin
/**
 * 基于ksp生成的路由表导航
 */
@Composable
fun renderNavContent(navController: NavHostController) {
    NavHost(
        navController = navController,
        startDestination = RouteKeys.HOME_SCREEN,
        modifier = Modifier.fillMaxSize().padding(16.dp)
    ) {
        // 动态生成导航目标
        RouteTable.allRoutes.forEach { (route, content) ->
            composable(route) {
                content()
            }
        }
    }

    NavgationService.initialize(navController)


}

```

# feature: RBAC完善,组件封装,AI智能体

[//]: # (## Acknowledgments)

[//]: # (Thanks to [JetBrains]&#40;https://www.jetbrains.com&#41; for providing free licenses to open source projects.)

[<img src="https://resources.jetbrains.com/storage/products/company/brand/logos/jb_beam.svg" width="100">](https://www.jetbrains.com)
