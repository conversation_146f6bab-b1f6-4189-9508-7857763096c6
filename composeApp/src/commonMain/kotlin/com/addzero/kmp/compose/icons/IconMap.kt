
package com.addzero.kmp.compose.icons

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.*
import androidx.compose.material.icons.automirrored.outlined.*
import androidx.compose.material.icons.filled.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.vector.ImageVector

/**
 * 图标数据类，包含图标的键名、类型和实际的ImageVector对象
 */
data class IconData(
    val iconKey: String,
    val iconType: String,
    val vector: ImageVector
)

/**
 * 自动生成的图标访问工具类
 */
object IconMap {
    /**
     * 所有图标数据的序列
     */
    val allIcons = sequenceOf(
        IconData("Abc", "Filled", Icons.Filled.Abc),
        IconData("AcUnit", "Filled", Icons.Filled.AcUnit),
        IconData("AccessAlarm", "Filled", Icons.Filled.AccessAlarm),
        IconData("AccessAlarms", "Filled", Icons.Filled.AccessAlarms),
        IconData("AccessTime", "Filled", Icons.Filled.AccessTime),
        IconData("AccessTimeFilled", "Filled", Icons.Filled.AccessTimeFilled),
        IconData("Accessibility", "Filled", Icons.Filled.Accessibility),
        IconData("AccessibilityNew", "Filled", Icons.Filled.AccessibilityNew),
        IconData("AccountBalance", "Filled", Icons.Filled.AccountBalance),
        IconData("AccountBalanceWallet", "Filled", Icons.Filled.AccountBalanceWallet),
        IconData("AccountTree", "Filled", Icons.Filled.AccountTree),
        IconData("AdUnits", "Filled", Icons.Filled.AdUnits),
        IconData("Adb", "Filled", Icons.Filled.Adb),
        IconData("AddAPhoto", "Filled", Icons.Filled.AddAPhoto),
        IconData("AddAlarm", "Filled", Icons.Filled.AddAlarm),
        IconData("AddAlert", "Filled", Icons.Filled.AddAlert),
        IconData("AddBox", "Filled", Icons.Filled.AddBox),
        IconData("AddBusiness", "Filled", Icons.Filled.AddBusiness),
        IconData("AddCard", "Filled", Icons.Filled.AddCard),
        IconData("AddChart", "Filled", Icons.Filled.AddChart),
        IconData("AddCircleOutline", "Filled", Icons.Filled.AddCircleOutline),
        IconData("AddComment", "Filled", Icons.Filled.AddComment),
        IconData("AddHome", "Filled", Icons.Filled.AddHome),
        IconData("AddHomeWork", "Filled", Icons.Filled.AddHomeWork),
        IconData("AddIcCall", "Filled", Icons.Filled.AddIcCall),
        IconData("AddLink", "Filled", Icons.Filled.AddLink),
        IconData("AddLocation", "Filled", Icons.Filled.AddLocation),
        IconData("AddLocationAlt", "Filled", Icons.Filled.AddLocationAlt),
        IconData("AddModerator", "Filled", Icons.Filled.AddModerator),
        IconData("AddPhotoAlternate", "Filled", Icons.Filled.AddPhotoAlternate),
        IconData("AddReaction", "Filled", Icons.Filled.AddReaction),
        IconData("AddRoad", "Filled", Icons.Filled.AddRoad),
        IconData("AddShoppingCart", "Filled", Icons.Filled.AddShoppingCart),
        IconData("AddTask", "Filled", Icons.Filled.AddTask),
        IconData("AddToDrive", "Filled", Icons.Filled.AddToDrive),
        IconData("AddToPhotos", "Filled", Icons.Filled.AddToPhotos),
        IconData("AddToQueue", "Filled", Icons.Filled.AddToQueue),
        IconData("Addchart", "Filled", Icons.Filled.Addchart),
        IconData("AdfScanner", "Filled", Icons.Filled.AdfScanner),
        IconData("Adjust", "Filled", Icons.Filled.Adjust),
        IconData("AdminPanelSettings", "Filled", Icons.Filled.AdminPanelSettings),
        IconData("AdsClick", "Filled", Icons.Filled.AdsClick),
        IconData("Agriculture", "Filled", Icons.Filled.Agriculture),
        IconData("Air", "Filled", Icons.Filled.Air),
        IconData("AirlineSeatFlat", "Filled", Icons.Filled.AirlineSeatFlat),
        IconData("AirlineSeatFlatAngled", "Filled", Icons.Filled.AirlineSeatFlatAngled),
        IconData("AirlineSeatIndividualSuite", "Filled", Icons.Filled.AirlineSeatIndividualSuite),
        IconData("AirlineSeatLegroomExtra", "Filled", Icons.Filled.AirlineSeatLegroomExtra),
        IconData("AirlineSeatLegroomNormal", "Filled", Icons.Filled.AirlineSeatLegroomNormal),
        IconData("AirlineSeatLegroomReduced", "Filled", Icons.Filled.AirlineSeatLegroomReduced),
        IconData("AirlineSeatReclineExtra", "Filled", Icons.Filled.AirlineSeatReclineExtra),
        IconData("AirlineSeatReclineNormal", "Filled", Icons.Filled.AirlineSeatReclineNormal),
        IconData("AirlineStops", "Filled", Icons.Filled.AirlineStops),
        IconData("Airlines", "Filled", Icons.Filled.Airlines),
        IconData("AirplanemodeActive", "Filled", Icons.Filled.AirplanemodeActive),
        IconData("AirplanemodeInactive", "Filled", Icons.Filled.AirplanemodeInactive),
        IconData("Airplay", "Filled", Icons.Filled.Airplay),
        IconData("AirportShuttle", "Filled", Icons.Filled.AirportShuttle),
        IconData("Alarm", "Filled", Icons.Filled.Alarm),
        IconData("AlarmAdd", "Filled", Icons.Filled.AlarmAdd),
        IconData("AlarmOff", "Filled", Icons.Filled.AlarmOff),
        IconData("AlarmOn", "Filled", Icons.Filled.AlarmOn),
        IconData("Album", "Filled", Icons.Filled.Album),
        IconData("AlignHorizontalCenter", "Filled", Icons.Filled.AlignHorizontalCenter),
        IconData("AlignVerticalBottom", "Filled", Icons.Filled.AlignVerticalBottom),
        IconData("AlignVerticalCenter", "Filled", Icons.Filled.AlignVerticalCenter),
        IconData("AlignVerticalTop", "Filled", Icons.Filled.AlignVerticalTop),
        IconData("AllInbox", "Filled", Icons.Filled.AllInbox),
        IconData("AllInclusive", "Filled", Icons.Filled.AllInclusive),
        IconData("AllOut", "Filled", Icons.Filled.AllOut),
        IconData("AlternateEmail", "Filled", Icons.Filled.AlternateEmail),
        IconData("AmpStories", "Filled", Icons.Filled.AmpStories),
        IconData("Analytics", "Filled", Icons.Filled.Analytics),
        IconData("Anchor", "Filled", Icons.Filled.Anchor),
        IconData("Android", "Filled", Icons.Filled.Android),
        IconData("Animation", "Filled", Icons.Filled.Animation),
        IconData("Aod", "Filled", Icons.Filled.Aod),
        IconData("Apartment", "Filled", Icons.Filled.Apartment),
        IconData("Api", "Filled", Icons.Filled.Api),
        IconData("AppBlocking", "Filled", Icons.Filled.AppBlocking),
        IconData("AppRegistration", "Filled", Icons.Filled.AppRegistration),
        IconData("AppSettingsAlt", "Filled", Icons.Filled.AppSettingsAlt),
        IconData("AppShortcut", "Filled", Icons.Filled.AppShortcut),
        IconData("Approval", "Filled", Icons.Filled.Approval),
        IconData("Apps", "Filled", Icons.Filled.Apps),
        IconData("AppsOutage", "Filled", Icons.Filled.AppsOutage),
        IconData("Architecture", "Filled", Icons.Filled.Architecture),
        IconData("Archive", "Filled", Icons.Filled.Archive),
        IconData("AreaChart", "Filled", Icons.Filled.AreaChart),
        IconData("ArrowBackIosNew", "Filled", Icons.Filled.ArrowBackIosNew),
        IconData("ArrowCircleDown", "Filled", Icons.Filled.ArrowCircleDown),
        IconData("ArrowCircleLeft", "Filled", Icons.Filled.ArrowCircleLeft),
        IconData("ArrowCircleRight", "Filled", Icons.Filled.ArrowCircleRight),
        IconData("ArrowCircleUp", "Filled", Icons.Filled.ArrowCircleUp),
        IconData("ArrowDownward", "Filled", Icons.Filled.ArrowDownward),
        IconData("ArrowDropDownCircle", "Filled", Icons.Filled.ArrowDropDownCircle),
        IconData("ArrowDropUp", "Filled", Icons.Filled.ArrowDropUp),
        IconData("ArrowOutward", "Filled", Icons.Filled.ArrowOutward),
        IconData("ArrowUpward", "Filled", Icons.Filled.ArrowUpward),
        IconData("ArtTrack", "Filled", Icons.Filled.ArtTrack),
        IconData("AspectRatio", "Filled", Icons.Filled.AspectRatio),
        IconData("Assessment", "Filled", Icons.Filled.Assessment),
        IconData("AssignmentInd", "Filled", Icons.Filled.AssignmentInd),
        IconData("AssignmentLate", "Filled", Icons.Filled.AssignmentLate),
        IconData("AssignmentReturned", "Filled", Icons.Filled.AssignmentReturned),
        IconData("AssignmentTurnedIn", "Filled", Icons.Filled.AssignmentTurnedIn),
        IconData("AssistWalker", "Filled", Icons.Filled.AssistWalker),
        IconData("Assistant", "Filled", Icons.Filled.Assistant),
        IconData("AssistantPhoto", "Filled", Icons.Filled.AssistantPhoto),
        IconData("AssuredWorkload", "Filled", Icons.Filled.AssuredWorkload),
        IconData("Atm", "Filled", Icons.Filled.Atm),
        IconData("AttachEmail", "Filled", Icons.Filled.AttachEmail),
        IconData("AttachFile", "Filled", Icons.Filled.AttachFile),
        IconData("AttachMoney", "Filled", Icons.Filled.AttachMoney),
        IconData("Attachment", "Filled", Icons.Filled.Attachment),
        IconData("Attractions", "Filled", Icons.Filled.Attractions),
        IconData("Attribution", "Filled", Icons.Filled.Attribution),
        IconData("AudioFile", "Filled", Icons.Filled.AudioFile),
        IconData("Audiotrack", "Filled", Icons.Filled.Audiotrack),
        IconData("AutoAwesome", "Filled", Icons.Filled.AutoAwesome),
        IconData("AutoAwesomeMosaic", "Filled", Icons.Filled.AutoAwesomeMosaic),
        IconData("AutoAwesomeMotion", "Filled", Icons.Filled.AutoAwesomeMotion),
        IconData("AutoDelete", "Filled", Icons.Filled.AutoDelete),
        IconData("AutoFixHigh", "Filled", Icons.Filled.AutoFixHigh),
        IconData("AutoFixNormal", "Filled", Icons.Filled.AutoFixNormal),
        IconData("AutoFixOff", "Filled", Icons.Filled.AutoFixOff),
        IconData("AutoGraph", "Filled", Icons.Filled.AutoGraph),
        IconData("AutoMode", "Filled", Icons.Filled.AutoMode),
        IconData("AutoStories", "Filled", Icons.Filled.AutoStories),
        IconData("AutofpsSelect", "Filled", Icons.Filled.AutofpsSelect),
        IconData("Autorenew", "Filled", Icons.Filled.Autorenew),
        IconData("AvTimer", "Filled", Icons.Filled.AvTimer),
        IconData("BabyChangingStation", "Filled", Icons.Filled.BabyChangingStation),
        IconData("BackHand", "Filled", Icons.Filled.BackHand),
        IconData("Backpack", "Filled", Icons.Filled.Backpack),
        IconData("Backup", "Filled", Icons.Filled.Backup),
        IconData("BackupTable", "Filled", Icons.Filled.BackupTable),
        IconData("Badge", "Filled", Icons.Filled.Badge),
        IconData("BakeryDining", "Filled", Icons.Filled.BakeryDining),
        IconData("Balance", "Filled", Icons.Filled.Balance),
        IconData("Balcony", "Filled", Icons.Filled.Balcony),
        IconData("Ballot", "Filled", Icons.Filled.Ballot),
        IconData("BarChart", "Filled", Icons.Filled.BarChart),
        IconData("BatchPrediction", "Filled", Icons.Filled.BatchPrediction),
        IconData("Bathroom", "Filled", Icons.Filled.Bathroom),
        IconData("Bathtub", "Filled", Icons.Filled.Bathtub),
        IconData("Battery0Bar", "Filled", Icons.Filled.Battery0Bar),
        IconData("Battery1Bar", "Filled", Icons.Filled.Battery1Bar),
        IconData("Battery2Bar", "Filled", Icons.Filled.Battery2Bar),
        IconData("Battery3Bar", "Filled", Icons.Filled.Battery3Bar),
        IconData("Battery4Bar", "Filled", Icons.Filled.Battery4Bar),
        IconData("Battery5Bar", "Filled", Icons.Filled.Battery5Bar),
        IconData("Battery6Bar", "Filled", Icons.Filled.Battery6Bar),
        IconData("BatteryAlert", "Filled", Icons.Filled.BatteryAlert),
        IconData("BatteryChargingFull", "Filled", Icons.Filled.BatteryChargingFull),
        IconData("BatteryFull", "Filled", Icons.Filled.BatteryFull),
        IconData("BatterySaver", "Filled", Icons.Filled.BatterySaver),
        IconData("BatteryStd", "Filled", Icons.Filled.BatteryStd),
        IconData("BeachAccess", "Filled", Icons.Filled.BeachAccess),
        IconData("Bed", "Filled", Icons.Filled.Bed),
        IconData("BedroomBaby", "Filled", Icons.Filled.BedroomBaby),
        IconData("BedroomChild", "Filled", Icons.Filled.BedroomChild),
        IconData("BedroomParent", "Filled", Icons.Filled.BedroomParent),
        IconData("Bedtime", "Filled", Icons.Filled.Bedtime),
        IconData("BedtimeOff", "Filled", Icons.Filled.BedtimeOff),
        IconData("Beenhere", "Filled", Icons.Filled.Beenhere),
        IconData("Bento", "Filled", Icons.Filled.Bento),
        IconData("BikeScooter", "Filled", Icons.Filled.BikeScooter),
        IconData("Biotech", "Filled", Icons.Filled.Biotech),
        IconData("Blender", "Filled", Icons.Filled.Blender),
        IconData("Blind", "Filled", Icons.Filled.Blind),
        IconData("Blinds", "Filled", Icons.Filled.Blinds),
        IconData("BlindsClosed", "Filled", Icons.Filled.BlindsClosed),
        IconData("Block", "Filled", Icons.Filled.Block),
        IconData("Bloodtype", "Filled", Icons.Filled.Bloodtype),
        IconData("Bluetooth", "Filled", Icons.Filled.Bluetooth),
        IconData("BluetoothAudio", "Filled", Icons.Filled.BluetoothAudio),
        IconData("BluetoothConnected", "Filled", Icons.Filled.BluetoothConnected),
        IconData("BluetoothDisabled", "Filled", Icons.Filled.BluetoothDisabled),
        IconData("BluetoothDrive", "Filled", Icons.Filled.BluetoothDrive),
        IconData("BlurCircular", "Filled", Icons.Filled.BlurCircular),
        IconData("BlurLinear", "Filled", Icons.Filled.BlurLinear),
        IconData("BlurOff", "Filled", Icons.Filled.BlurOff),
        IconData("BlurOn", "Filled", Icons.Filled.BlurOn),
        IconData("Bolt", "Filled", Icons.Filled.Bolt),
        IconData("Book", "Filled", Icons.Filled.Book),
        IconData("BookOnline", "Filled", Icons.Filled.BookOnline),
        IconData("Bookmark", "Filled", Icons.Filled.Bookmark),
        IconData("BookmarkAdd", "Filled", Icons.Filled.BookmarkAdd),
        IconData("BookmarkAdded", "Filled", Icons.Filled.BookmarkAdded),
        IconData("BookmarkBorder", "Filled", Icons.Filled.BookmarkBorder),
        IconData("BookmarkRemove", "Filled", Icons.Filled.BookmarkRemove),
        IconData("Bookmarks", "Filled", Icons.Filled.Bookmarks),
        IconData("BorderAll", "Filled", Icons.Filled.BorderAll),
        IconData("BorderBottom", "Filled", Icons.Filled.BorderBottom),
        IconData("BorderClear", "Filled", Icons.Filled.BorderClear),
        IconData("BorderColor", "Filled", Icons.Filled.BorderColor),
        IconData("BorderHorizontal", "Filled", Icons.Filled.BorderHorizontal),
        IconData("BorderInner", "Filled", Icons.Filled.BorderInner),
        IconData("BorderLeft", "Filled", Icons.Filled.BorderLeft),
        IconData("BorderOuter", "Filled", Icons.Filled.BorderOuter),
        IconData("BorderRight", "Filled", Icons.Filled.BorderRight),
        IconData("BorderStyle", "Filled", Icons.Filled.BorderStyle),
        IconData("BorderTop", "Filled", Icons.Filled.BorderTop),
        IconData("BorderVertical", "Filled", Icons.Filled.BorderVertical),
        IconData("Boy", "Filled", Icons.Filled.Boy),
        IconData("BreakfastDining", "Filled", Icons.Filled.BreakfastDining),
        IconData("Brightness1", "Filled", Icons.Filled.Brightness1),
        IconData("Brightness2", "Filled", Icons.Filled.Brightness2),
        IconData("Brightness3", "Filled", Icons.Filled.Brightness3),
        IconData("Brightness4", "Filled", Icons.Filled.Brightness4),
        IconData("Brightness5", "Filled", Icons.Filled.Brightness5),
        IconData("Brightness6", "Filled", Icons.Filled.Brightness6),
        IconData("Brightness7", "Filled", Icons.Filled.Brightness7),
        IconData("BrightnessAuto", "Filled", Icons.Filled.BrightnessAuto),
        IconData("BrightnessHigh", "Filled", Icons.Filled.BrightnessHigh),
        IconData("BrightnessLow", "Filled", Icons.Filled.BrightnessLow),
        IconData("BrightnessMedium", "Filled", Icons.Filled.BrightnessMedium),
        IconData("BroadcastOnHome", "Filled", Icons.Filled.BroadcastOnHome),
        IconData("BroadcastOnPersonal", "Filled", Icons.Filled.BroadcastOnPersonal),
        IconData("BrokenImage", "Filled", Icons.Filled.BrokenImage),
        IconData("BrowseGallery", "Filled", Icons.Filled.BrowseGallery),
        IconData("BrowserNotSupported", "Filled", Icons.Filled.BrowserNotSupported),
        IconData("BrowserUpdated", "Filled", Icons.Filled.BrowserUpdated),
        IconData("BrunchDining", "Filled", Icons.Filled.BrunchDining),
        IconData("Brush", "Filled", Icons.Filled.Brush),
        IconData("BubbleChart", "Filled", Icons.Filled.BubbleChart),
        IconData("BugReport", "Filled", Icons.Filled.BugReport),
        IconData("BuildCircle", "Filled", Icons.Filled.BuildCircle),
        IconData("Bungalow", "Filled", Icons.Filled.Bungalow),
        IconData("BurstMode", "Filled", Icons.Filled.BurstMode),
        IconData("BusAlert", "Filled", Icons.Filled.BusAlert),
        IconData("Business", "Filled", Icons.Filled.Business),
        IconData("BusinessCenter", "Filled", Icons.Filled.BusinessCenter),
        IconData("Cabin", "Filled", Icons.Filled.Cabin),
        IconData("Cable", "Filled", Icons.Filled.Cable),
        IconData("Cached", "Filled", Icons.Filled.Cached),
        IconData("Cake", "Filled", Icons.Filled.Cake),
        IconData("Calculate", "Filled", Icons.Filled.Calculate),
        IconData("CalendarMonth", "Filled", Icons.Filled.CalendarMonth),
        IconData("CalendarToday", "Filled", Icons.Filled.CalendarToday),
        IconData("CalendarViewDay", "Filled", Icons.Filled.CalendarViewDay),
        IconData("CalendarViewMonth", "Filled", Icons.Filled.CalendarViewMonth),
        IconData("CalendarViewWeek", "Filled", Icons.Filled.CalendarViewWeek),
        IconData("CallEnd", "Filled", Icons.Filled.CallEnd),
        IconData("CallToAction", "Filled", Icons.Filled.CallToAction),
        IconData("Camera", "Filled", Icons.Filled.Camera),
        IconData("CameraAlt", "Filled", Icons.Filled.CameraAlt),
        IconData("CameraEnhance", "Filled", Icons.Filled.CameraEnhance),
        IconData("CameraFront", "Filled", Icons.Filled.CameraFront),
        IconData("CameraIndoor", "Filled", Icons.Filled.CameraIndoor),
        IconData("CameraOutdoor", "Filled", Icons.Filled.CameraOutdoor),
        IconData("CameraRear", "Filled", Icons.Filled.CameraRear),
        IconData("CameraRoll", "Filled", Icons.Filled.CameraRoll),
        IconData("Cameraswitch", "Filled", Icons.Filled.Cameraswitch),
        IconData("Campaign", "Filled", Icons.Filled.Campaign),
        IconData("Cancel", "Filled", Icons.Filled.Cancel),
        IconData("CancelPresentation", "Filled", Icons.Filled.CancelPresentation),
        IconData("CancelScheduleSend", "Filled", Icons.Filled.CancelScheduleSend),
        IconData("CandlestickChart", "Filled", Icons.Filled.CandlestickChart),
        IconData("CarCrash", "Filled", Icons.Filled.CarCrash),
        IconData("CarRental", "Filled", Icons.Filled.CarRental),
        IconData("CarRepair", "Filled", Icons.Filled.CarRepair),
        IconData("CardGiftcard", "Filled", Icons.Filled.CardGiftcard),
        IconData("CardMembership", "Filled", Icons.Filled.CardMembership),
        IconData("CardTravel", "Filled", Icons.Filled.CardTravel),
        IconData("Carpenter", "Filled", Icons.Filled.Carpenter),
        IconData("Cases", "Filled", Icons.Filled.Cases),
        IconData("Casino", "Filled", Icons.Filled.Casino),
        IconData("Cast", "Filled", Icons.Filled.Cast),
        IconData("CastConnected", "Filled", Icons.Filled.CastConnected),
        IconData("CastForEducation", "Filled", Icons.Filled.CastForEducation),
        IconData("Castle", "Filled", Icons.Filled.Castle),
        IconData("CatchingPokemon", "Filled", Icons.Filled.CatchingPokemon),
        IconData("Category", "Filled", Icons.Filled.Category),
        IconData("Celebration", "Filled", Icons.Filled.Celebration),
        IconData("CellTower", "Filled", Icons.Filled.CellTower),
        IconData("CellWifi", "Filled", Icons.Filled.CellWifi),
        IconData("CenterFocusStrong", "Filled", Icons.Filled.CenterFocusStrong),
        IconData("CenterFocusWeak", "Filled", Icons.Filled.CenterFocusWeak),
        IconData("Chair", "Filled", Icons.Filled.Chair),
        IconData("ChairAlt", "Filled", Icons.Filled.ChairAlt),
        IconData("Chalet", "Filled", Icons.Filled.Chalet),
        IconData("ChangeCircle", "Filled", Icons.Filled.ChangeCircle),
        IconData("ChangeHistory", "Filled", Icons.Filled.ChangeHistory),
        IconData("ChargingStation", "Filled", Icons.Filled.ChargingStation),
        IconData("ChatBubble", "Filled", Icons.Filled.ChatBubble),
        IconData("ChatBubbleOutline", "Filled", Icons.Filled.ChatBubbleOutline),
        IconData("CheckBox", "Filled", Icons.Filled.CheckBox),
        IconData("CheckBoxOutlineBlank", "Filled", Icons.Filled.CheckBoxOutlineBlank),
        IconData("CheckCircleOutline", "Filled", Icons.Filled.CheckCircleOutline),
        IconData("Checklist", "Filled", Icons.Filled.Checklist),
        IconData("ChecklistRtl", "Filled", Icons.Filled.ChecklistRtl),
        IconData("Checkroom", "Filled", Icons.Filled.Checkroom),
        IconData("ChevronLeft", "Filled", Icons.Filled.ChevronLeft),
        IconData("ChevronRight", "Filled", Icons.Filled.ChevronRight),
        IconData("ChildCare", "Filled", Icons.Filled.ChildCare),
        IconData("ChildFriendly", "Filled", Icons.Filled.ChildFriendly),
        IconData("Church", "Filled", Icons.Filled.Church),
        IconData("Circle", "Filled", Icons.Filled.Circle),
        IconData("CircleNotifications", "Filled", Icons.Filled.CircleNotifications),
        IconData("Class", "Filled", Icons.Filled.Class),
        IconData("CleanHands", "Filled", Icons.Filled.CleanHands),
        IconData("CleaningServices", "Filled", Icons.Filled.CleaningServices),
        IconData("ClearAll", "Filled", Icons.Filled.ClearAll),
        IconData("CloseFullscreen", "Filled", Icons.Filled.CloseFullscreen),
        IconData("ClosedCaption", "Filled", Icons.Filled.ClosedCaption),
        IconData("ClosedCaptionDisabled", "Filled", Icons.Filled.ClosedCaptionDisabled),
        IconData("ClosedCaptionOff", "Filled", Icons.Filled.ClosedCaptionOff),
        IconData("Cloud", "Filled", Icons.Filled.Cloud),
        IconData("CloudCircle", "Filled", Icons.Filled.CloudCircle),
        IconData("CloudDone", "Filled", Icons.Filled.CloudDone),
        IconData("CloudDownload", "Filled", Icons.Filled.CloudDownload),
        IconData("CloudOff", "Filled", Icons.Filled.CloudOff),
        IconData("CloudQueue", "Filled", Icons.Filled.CloudQueue),
        IconData("CloudSync", "Filled", Icons.Filled.CloudSync),
        IconData("CloudUpload", "Filled", Icons.Filled.CloudUpload),
        IconData("Co2", "Filled", Icons.Filled.Co2),
        IconData("CoPresent", "Filled", Icons.Filled.CoPresent),
        IconData("Code", "Filled", Icons.Filled.Code),
        IconData("CodeOff", "Filled", Icons.Filled.CodeOff),
        IconData("Coffee", "Filled", Icons.Filled.Coffee),
        IconData("CoffeeMaker", "Filled", Icons.Filled.CoffeeMaker),
        IconData("Collections", "Filled", Icons.Filled.Collections),
        IconData("CollectionsBookmark", "Filled", Icons.Filled.CollectionsBookmark),
        IconData("ColorLens", "Filled", Icons.Filled.ColorLens),
        IconData("Colorize", "Filled", Icons.Filled.Colorize),
        IconData("CommentBank", "Filled", Icons.Filled.CommentBank),
        IconData("CommentsDisabled", "Filled", Icons.Filled.CommentsDisabled),
        IconData("Commit", "Filled", Icons.Filled.Commit),
        IconData("Commute", "Filled", Icons.Filled.Commute),
        IconData("Compare", "Filled", Icons.Filled.Compare),
        IconData("CompassCalibration", "Filled", Icons.Filled.CompassCalibration),
        IconData("Compost", "Filled", Icons.Filled.Compost),
        IconData("Compress", "Filled", Icons.Filled.Compress),
        IconData("Computer", "Filled", Icons.Filled.Computer),
        IconData("ConfirmationNumber", "Filled", Icons.Filled.ConfirmationNumber),
        IconData("ConnectWithoutContact", "Filled", Icons.Filled.ConnectWithoutContact),
        IconData("ConnectedTv", "Filled", Icons.Filled.ConnectedTv),
        IconData("ConnectingAirports", "Filled", Icons.Filled.ConnectingAirports),
        IconData("Construction", "Filled", Icons.Filled.Construction),
        IconData("ContactEmergency", "Filled", Icons.Filled.ContactEmergency),
        IconData("ContactMail", "Filled", Icons.Filled.ContactMail),
        IconData("ContactPage", "Filled", Icons.Filled.ContactPage),
        IconData("ContactPhone", "Filled", Icons.Filled.ContactPhone),
        IconData("Contactless", "Filled", Icons.Filled.Contactless),
        IconData("Contacts", "Filled", Icons.Filled.Contacts),
        IconData("ContentCopy", "Filled", Icons.Filled.ContentCopy),
        IconData("ContentCut", "Filled", Icons.Filled.ContentCut),
        IconData("ContentPaste", "Filled", Icons.Filled.ContentPaste),
        IconData("ContentPasteGo", "Filled", Icons.Filled.ContentPasteGo),
        IconData("ContentPasteOff", "Filled", Icons.Filled.ContentPasteOff),
        IconData("ContentPasteSearch", "Filled", Icons.Filled.ContentPasteSearch),
        IconData("Contrast", "Filled", Icons.Filled.Contrast),
        IconData("ControlCamera", "Filled", Icons.Filled.ControlCamera),
        IconData("ControlPoint", "Filled", Icons.Filled.ControlPoint),
        IconData("ControlPointDuplicate", "Filled", Icons.Filled.ControlPointDuplicate),
        IconData("Cookie", "Filled", Icons.Filled.Cookie),
        IconData("CopyAll", "Filled", Icons.Filled.CopyAll),
        IconData("Copyright", "Filled", Icons.Filled.Copyright),
        IconData("Coronavirus", "Filled", Icons.Filled.Coronavirus),
        IconData("CorporateFare", "Filled", Icons.Filled.CorporateFare),
        IconData("Cottage", "Filled", Icons.Filled.Cottage),
        IconData("Countertops", "Filled", Icons.Filled.Countertops),
        IconData("CreateNewFolder", "Filled", Icons.Filled.CreateNewFolder),
        IconData("CreditCard", "Filled", Icons.Filled.CreditCard),
        IconData("CreditCardOff", "Filled", Icons.Filled.CreditCardOff),
        IconData("CreditScore", "Filled", Icons.Filled.CreditScore),
        IconData("Crib", "Filled", Icons.Filled.Crib),
        IconData("CrisisAlert", "Filled", Icons.Filled.CrisisAlert),
        IconData("Crop", "Filled", Icons.Filled.Crop),
        IconData("Crop169", "Filled", Icons.Filled.Crop169),
        IconData("Crop32", "Filled", Icons.Filled.Crop32),
        IconData("Crop54", "Filled", Icons.Filled.Crop54),
        IconData("Crop75", "Filled", Icons.Filled.Crop75),
        IconData("CropDin", "Filled", Icons.Filled.CropDin),
        IconData("CropFree", "Filled", Icons.Filled.CropFree),
        IconData("CropLandscape", "Filled", Icons.Filled.CropLandscape),
        IconData("CropOriginal", "Filled", Icons.Filled.CropOriginal),
        IconData("CropPortrait", "Filled", Icons.Filled.CropPortrait),
        IconData("CropRotate", "Filled", Icons.Filled.CropRotate),
        IconData("CropSquare", "Filled", Icons.Filled.CropSquare),
        IconData("CrueltyFree", "Filled", Icons.Filled.CrueltyFree),
        IconData("Css", "Filled", Icons.Filled.Css),
        IconData("CurrencyBitcoin", "Filled", Icons.Filled.CurrencyBitcoin),
        IconData("CurrencyExchange", "Filled", Icons.Filled.CurrencyExchange),
        IconData("CurrencyFranc", "Filled", Icons.Filled.CurrencyFranc),
        IconData("CurrencyLira", "Filled", Icons.Filled.CurrencyLira),
        IconData("CurrencyPound", "Filled", Icons.Filled.CurrencyPound),
        IconData("CurrencyRuble", "Filled", Icons.Filled.CurrencyRuble),
        IconData("CurrencyRupee", "Filled", Icons.Filled.CurrencyRupee),
        IconData("CurrencyYen", "Filled", Icons.Filled.CurrencyYen),
        IconData("CurrencyYuan", "Filled", Icons.Filled.CurrencyYuan),
        IconData("Curtains", "Filled", Icons.Filled.Curtains),
        IconData("CurtainsClosed", "Filled", Icons.Filled.CurtainsClosed),
        IconData("Cyclone", "Filled", Icons.Filled.Cyclone),
        IconData("Dangerous", "Filled", Icons.Filled.Dangerous),
        IconData("DarkMode", "Filled", Icons.Filled.DarkMode),
        IconData("Dashboard", "Filled", Icons.Filled.Dashboard),
        IconData("DashboardCustomize", "Filled", Icons.Filled.DashboardCustomize),
        IconData("DataArray", "Filled", Icons.Filled.DataArray),
        IconData("DataExploration", "Filled", Icons.Filled.DataExploration),
        IconData("DataObject", "Filled", Icons.Filled.DataObject),
        IconData("DataSaverOff", "Filled", Icons.Filled.DataSaverOff),
        IconData("DataSaverOn", "Filled", Icons.Filled.DataSaverOn),
        IconData("DataThresholding", "Filled", Icons.Filled.DataThresholding),
        IconData("DataUsage", "Filled", Icons.Filled.DataUsage),
        IconData("Dataset", "Filled", Icons.Filled.Dataset),
        IconData("DatasetLinked", "Filled", Icons.Filled.DatasetLinked),
        IconData("Deblur", "Filled", Icons.Filled.Deblur),
        IconData("Deck", "Filled", Icons.Filled.Deck),
        IconData("Dehaze", "Filled", Icons.Filled.Dehaze),
        IconData("DeleteForever", "Filled", Icons.Filled.DeleteForever),
        IconData("DeleteOutline", "Filled", Icons.Filled.DeleteOutline),
        IconData("DeleteSweep", "Filled", Icons.Filled.DeleteSweep),
        IconData("DeliveryDining", "Filled", Icons.Filled.DeliveryDining),
        IconData("DensityLarge", "Filled", Icons.Filled.DensityLarge),
        IconData("DensityMedium", "Filled", Icons.Filled.DensityMedium),
        IconData("DensitySmall", "Filled", Icons.Filled.DensitySmall),
        IconData("DepartureBoard", "Filled", Icons.Filled.DepartureBoard),
        IconData("Description", "Filled", Icons.Filled.Description),
        IconData("Deselect", "Filled", Icons.Filled.Deselect),
        IconData("DesignServices", "Filled", Icons.Filled.DesignServices),
        IconData("Desk", "Filled", Icons.Filled.Desk),
        IconData("DesktopAccessDisabled", "Filled", Icons.Filled.DesktopAccessDisabled),
        IconData("DesktopMac", "Filled", Icons.Filled.DesktopMac),
        IconData("DesktopWindows", "Filled", Icons.Filled.DesktopWindows),
        IconData("Details", "Filled", Icons.Filled.Details),
        IconData("DeveloperBoard", "Filled", Icons.Filled.DeveloperBoard),
        IconData("DeveloperBoardOff", "Filled", Icons.Filled.DeveloperBoardOff),
        IconData("DeveloperMode", "Filled", Icons.Filled.DeveloperMode),
        IconData("DeviceHub", "Filled", Icons.Filled.DeviceHub),
        IconData("DeviceThermostat", "Filled", Icons.Filled.DeviceThermostat),
        IconData("DeviceUnknown", "Filled", Icons.Filled.DeviceUnknown),
        IconData("Devices", "Filled", Icons.Filled.Devices),
        IconData("DevicesFold", "Filled", Icons.Filled.DevicesFold),
        IconData("DevicesOther", "Filled", Icons.Filled.DevicesOther),
        IconData("DialerSip", "Filled", Icons.Filled.DialerSip),
        IconData("Dialpad", "Filled", Icons.Filled.Dialpad),
        IconData("Diamond", "Filled", Icons.Filled.Diamond),
        IconData("Difference", "Filled", Icons.Filled.Difference),
        IconData("Dining", "Filled", Icons.Filled.Dining),
        IconData("DinnerDining", "Filled", Icons.Filled.DinnerDining),
        IconData("Directions", "Filled", Icons.Filled.Directions),
        IconData("DirectionsBoat", "Filled", Icons.Filled.DirectionsBoat),
        IconData("DirectionsBoatFilled", "Filled", Icons.Filled.DirectionsBoatFilled),
        IconData("DirectionsBus", "Filled", Icons.Filled.DirectionsBus),
        IconData("DirectionsBusFilled", "Filled", Icons.Filled.DirectionsBusFilled),
        IconData("DirectionsCar", "Filled", Icons.Filled.DirectionsCar),
        IconData("DirectionsCarFilled", "Filled", Icons.Filled.DirectionsCarFilled),
        IconData("DirectionsOff", "Filled", Icons.Filled.DirectionsOff),
        IconData("DirectionsRailway", "Filled", Icons.Filled.DirectionsRailway),
        IconData("DirectionsRailwayFilled", "Filled", Icons.Filled.DirectionsRailwayFilled),
        IconData("DirectionsSubway", "Filled", Icons.Filled.DirectionsSubway),
        IconData("DirectionsSubwayFilled", "Filled", Icons.Filled.DirectionsSubwayFilled),
        IconData("DirectionsTransit", "Filled", Icons.Filled.DirectionsTransit),
        IconData("DirectionsTransitFilled", "Filled", Icons.Filled.DirectionsTransitFilled),
        IconData("DirtyLens", "Filled", Icons.Filled.DirtyLens),
        IconData("DisabledByDefault", "Filled", Icons.Filled.DisabledByDefault),
        IconData("DisabledVisible", "Filled", Icons.Filled.DisabledVisible),
        IconData("DiscFull", "Filled", Icons.Filled.DiscFull),
        IconData("Discount", "Filled", Icons.Filled.Discount),
        IconData("DisplaySettings", "Filled", Icons.Filled.DisplaySettings),
        IconData("Diversity1", "Filled", Icons.Filled.Diversity1),
        IconData("Diversity2", "Filled", Icons.Filled.Diversity2),
        IconData("Diversity3", "Filled", Icons.Filled.Diversity3),
        IconData("Dns", "Filled", Icons.Filled.Dns),
        IconData("DoDisturb", "Filled", Icons.Filled.DoDisturb),
        IconData("DoDisturbAlt", "Filled", Icons.Filled.DoDisturbAlt),
        IconData("DoDisturbOff", "Filled", Icons.Filled.DoDisturbOff),
        IconData("DoDisturbOn", "Filled", Icons.Filled.DoDisturbOn),
        IconData("DoNotDisturb", "Filled", Icons.Filled.DoNotDisturb),
        IconData("DoNotDisturbAlt", "Filled", Icons.Filled.DoNotDisturbAlt),
        IconData("DoNotDisturbOff", "Filled", Icons.Filled.DoNotDisturbOff),
        IconData("DoNotDisturbOn", "Filled", Icons.Filled.DoNotDisturbOn),
        IconData("DoNotDisturbOnTotalSilence", "Filled", Icons.Filled.DoNotDisturbOnTotalSilence),
        IconData("DoNotStep", "Filled", Icons.Filled.DoNotStep),
        IconData("DoNotTouch", "Filled", Icons.Filled.DoNotTouch),
        IconData("Dock", "Filled", Icons.Filled.Dock),
        IconData("DocumentScanner", "Filled", Icons.Filled.DocumentScanner),
        IconData("Domain", "Filled", Icons.Filled.Domain),
        IconData("DomainAdd", "Filled", Icons.Filled.DomainAdd),
        IconData("DomainDisabled", "Filled", Icons.Filled.DomainDisabled),
        IconData("DomainVerification", "Filled", Icons.Filled.DomainVerification),
        IconData("DoneAll", "Filled", Icons.Filled.DoneAll),
        IconData("DoneOutline", "Filled", Icons.Filled.DoneOutline),
        IconData("DonutLarge", "Filled", Icons.Filled.DonutLarge),
        IconData("DonutSmall", "Filled", Icons.Filled.DonutSmall),
        IconData("DoorBack", "Filled", Icons.Filled.DoorBack),
        IconData("DoorFront", "Filled", Icons.Filled.DoorFront),
        IconData("DoorSliding", "Filled", Icons.Filled.DoorSliding),
        IconData("Doorbell", "Filled", Icons.Filled.Doorbell),
        IconData("DoubleArrow", "Filled", Icons.Filled.DoubleArrow),
        IconData("DownhillSkiing", "Filled", Icons.Filled.DownhillSkiing),
        IconData("Download", "Filled", Icons.Filled.Download),
        IconData("DownloadDone", "Filled", Icons.Filled.DownloadDone),
        IconData("DownloadForOffline", "Filled", Icons.Filled.DownloadForOffline),
        IconData("Downloading", "Filled", Icons.Filled.Downloading),
        IconData("Drafts", "Filled", Icons.Filled.Drafts),
        IconData("DragHandle", "Filled", Icons.Filled.DragHandle),
        IconData("DragIndicator", "Filled", Icons.Filled.DragIndicator),
        IconData("Draw", "Filled", Icons.Filled.Draw),
        IconData("DriveEta", "Filled", Icons.Filled.DriveEta),
        IconData("DriveFileMoveRtl", "Filled", Icons.Filled.DriveFileMoveRtl),
        IconData("DriveFileRenameOutline", "Filled", Icons.Filled.DriveFileRenameOutline),
        IconData("DriveFolderUpload", "Filled", Icons.Filled.DriveFolderUpload),
        IconData("Dry", "Filled", Icons.Filled.Dry),
        IconData("DryCleaning", "Filled", Icons.Filled.DryCleaning),
        IconData("Duo", "Filled", Icons.Filled.Duo),
        IconData("DynamicFeed", "Filled", Icons.Filled.DynamicFeed),
        IconData("DynamicForm", "Filled", Icons.Filled.DynamicForm),
        IconData("EMobiledata", "Filled", Icons.Filled.EMobiledata),
        IconData("Earbuds", "Filled", Icons.Filled.Earbuds),
        IconData("EarbudsBattery", "Filled", Icons.Filled.EarbudsBattery),
        IconData("East", "Filled", Icons.Filled.East),
        IconData("Eco", "Filled", Icons.Filled.Eco),
        IconData("EdgesensorHigh", "Filled", Icons.Filled.EdgesensorHigh),
        IconData("EdgesensorLow", "Filled", Icons.Filled.EdgesensorLow),
        IconData("EditAttributes", "Filled", Icons.Filled.EditAttributes),
        IconData("EditCalendar", "Filled", Icons.Filled.EditCalendar),
        IconData("EditLocation", "Filled", Icons.Filled.EditLocation),
        IconData("EditLocationAlt", "Filled", Icons.Filled.EditLocationAlt),
        IconData("EditNote", "Filled", Icons.Filled.EditNote),
        IconData("EditNotifications", "Filled", Icons.Filled.EditNotifications),
        IconData("EditOff", "Filled", Icons.Filled.EditOff),
        IconData("EditRoad", "Filled", Icons.Filled.EditRoad),
        IconData("Egg", "Filled", Icons.Filled.Egg),
        IconData("EggAlt", "Filled", Icons.Filled.EggAlt),
        IconData("Eject", "Filled", Icons.Filled.Eject),
        IconData("Elderly", "Filled", Icons.Filled.Elderly),
        IconData("ElderlyWoman", "Filled", Icons.Filled.ElderlyWoman),
        IconData("ElectricBike", "Filled", Icons.Filled.ElectricBike),
        IconData("ElectricBolt", "Filled", Icons.Filled.ElectricBolt),
        IconData("ElectricCar", "Filled", Icons.Filled.ElectricCar),
        IconData("ElectricMeter", "Filled", Icons.Filled.ElectricMeter),
        IconData("ElectricMoped", "Filled", Icons.Filled.ElectricMoped),
        IconData("ElectricRickshaw", "Filled", Icons.Filled.ElectricRickshaw),
        IconData("ElectricScooter", "Filled", Icons.Filled.ElectricScooter),
        IconData("ElectricalServices", "Filled", Icons.Filled.ElectricalServices),
        IconData("Elevator", "Filled", Icons.Filled.Elevator),
        IconData("Emergency", "Filled", Icons.Filled.Emergency),
        IconData("EmergencyRecording", "Filled", Icons.Filled.EmergencyRecording),
        IconData("EmergencyShare", "Filled", Icons.Filled.EmergencyShare),
        IconData("EmojiEmotions", "Filled", Icons.Filled.EmojiEmotions),
        IconData("EmojiEvents", "Filled", Icons.Filled.EmojiEvents),
        IconData("EmojiFlags", "Filled", Icons.Filled.EmojiFlags),
        IconData("EmojiFoodBeverage", "Filled", Icons.Filled.EmojiFoodBeverage),
        IconData("EmojiNature", "Filled", Icons.Filled.EmojiNature),
        IconData("EmojiObjects", "Filled", Icons.Filled.EmojiObjects),
        IconData("EmojiPeople", "Filled", Icons.Filled.EmojiPeople),
        IconData("EmojiSymbols", "Filled", Icons.Filled.EmojiSymbols),
        IconData("EmojiTransportation", "Filled", Icons.Filled.EmojiTransportation),
        IconData("EnergySavingsLeaf", "Filled", Icons.Filled.EnergySavingsLeaf),
        IconData("Engineering", "Filled", Icons.Filled.Engineering),
        IconData("EnhancedEncryption", "Filled", Icons.Filled.EnhancedEncryption),
        IconData("Equalizer", "Filled", Icons.Filled.Equalizer),
        IconData("Error", "Filled", Icons.Filled.Error),
        IconData("ErrorOutline", "Filled", Icons.Filled.ErrorOutline),
        IconData("Escalator", "Filled", Icons.Filled.Escalator),
        IconData("EscalatorWarning", "Filled", Icons.Filled.EscalatorWarning),
        IconData("Euro", "Filled", Icons.Filled.Euro),
        IconData("EuroSymbol", "Filled", Icons.Filled.EuroSymbol),
        IconData("EvStation", "Filled", Icons.Filled.EvStation),
        IconData("Event", "Filled", Icons.Filled.Event),
        IconData("EventAvailable", "Filled", Icons.Filled.EventAvailable),
        IconData("EventBusy", "Filled", Icons.Filled.EventBusy),
        IconData("EventRepeat", "Filled", Icons.Filled.EventRepeat),
        IconData("EventSeat", "Filled", Icons.Filled.EventSeat),
        IconData("Expand", "Filled", Icons.Filled.Expand),
        IconData("ExpandCircleDown", "Filled", Icons.Filled.ExpandCircleDown),
        IconData("ExpandLess", "Filled", Icons.Filled.ExpandLess),
        IconData("ExpandMore", "Filled", Icons.Filled.ExpandMore),
        IconData("Explicit", "Filled", Icons.Filled.Explicit),
        IconData("Explore", "Filled", Icons.Filled.Explore),
        IconData("ExploreOff", "Filled", Icons.Filled.ExploreOff),
        IconData("Exposure", "Filled", Icons.Filled.Exposure),
        IconData("ExposureNeg1", "Filled", Icons.Filled.ExposureNeg1),
        IconData("ExposureNeg2", "Filled", Icons.Filled.ExposureNeg2),
        IconData("ExposurePlus1", "Filled", Icons.Filled.ExposurePlus1),
        IconData("ExposurePlus2", "Filled", Icons.Filled.ExposurePlus2),
        IconData("ExposureZero", "Filled", Icons.Filled.ExposureZero),
        IconData("Extension", "Filled", Icons.Filled.Extension),
        IconData("ExtensionOff", "Filled", Icons.Filled.ExtensionOff),
        IconData("Face2", "Filled", Icons.Filled.Face2),
        IconData("Face3", "Filled", Icons.Filled.Face3),
        IconData("Face4", "Filled", Icons.Filled.Face4),
        IconData("Face5", "Filled", Icons.Filled.Face5),
        IconData("Face6", "Filled", Icons.Filled.Face6),
        IconData("FaceRetouchingNatural", "Filled", Icons.Filled.FaceRetouchingNatural),
        IconData("FaceRetouchingOff", "Filled", Icons.Filled.FaceRetouchingOff),
        IconData("Facebook", "Filled", Icons.Filled.Facebook),
        IconData("Factory", "Filled", Icons.Filled.Factory),
        IconData("FamilyRestroom", "Filled", Icons.Filled.FamilyRestroom),
        IconData("FastForward", "Filled", Icons.Filled.FastForward),
        IconData("FastRewind", "Filled", Icons.Filled.FastRewind),
        IconData("Fastfood", "Filled", Icons.Filled.Fastfood),
        IconData("Fax", "Filled", Icons.Filled.Fax),
        IconData("Feedback", "Filled", Icons.Filled.Feedback),
        IconData("Female", "Filled", Icons.Filled.Female),
        IconData("Fence", "Filled", Icons.Filled.Fence),
        IconData("Festival", "Filled", Icons.Filled.Festival),
        IconData("FiberDvr", "Filled", Icons.Filled.FiberDvr),
        IconData("FiberManualRecord", "Filled", Icons.Filled.FiberManualRecord),
        IconData("FiberNew", "Filled", Icons.Filled.FiberNew),
        IconData("FiberPin", "Filled", Icons.Filled.FiberPin),
        IconData("FiberSmartRecord", "Filled", Icons.Filled.FiberSmartRecord),
        IconData("FileCopy", "Filled", Icons.Filled.FileCopy),
        IconData("FileDownload", "Filled", Icons.Filled.FileDownload),
        IconData("FileDownloadDone", "Filled", Icons.Filled.FileDownloadDone),
        IconData("FileDownloadOff", "Filled", Icons.Filled.FileDownloadOff),
        IconData("FileOpen", "Filled", Icons.Filled.FileOpen),
        IconData("FilePresent", "Filled", Icons.Filled.FilePresent),
        IconData("FileUpload", "Filled", Icons.Filled.FileUpload),
        IconData("Filter", "Filled", Icons.Filled.Filter),
        IconData("Filter1", "Filled", Icons.Filled.Filter1),
        IconData("Filter2", "Filled", Icons.Filled.Filter2),
        IconData("Filter3", "Filled", Icons.Filled.Filter3),
        IconData("Filter4", "Filled", Icons.Filled.Filter4),
        IconData("Filter5", "Filled", Icons.Filled.Filter5),
        IconData("Filter6", "Filled", Icons.Filled.Filter6),
        IconData("Filter7", "Filled", Icons.Filled.Filter7),
        IconData("Filter8", "Filled", Icons.Filled.Filter8),
        IconData("Filter9", "Filled", Icons.Filled.Filter9),
        IconData("Filter9Plus", "Filled", Icons.Filled.Filter9Plus),
        IconData("FilterAlt", "Filled", Icons.Filled.FilterAlt),
        IconData("FilterAltOff", "Filled", Icons.Filled.FilterAltOff),
        IconData("FilterBAndW", "Filled", Icons.Filled.FilterBAndW),
        IconData("FilterCenterFocus", "Filled", Icons.Filled.FilterCenterFocus),
        IconData("FilterDrama", "Filled", Icons.Filled.FilterDrama),
        IconData("FilterFrames", "Filled", Icons.Filled.FilterFrames),
        IconData("FilterHdr", "Filled", Icons.Filled.FilterHdr),
        IconData("FilterList", "Filled", Icons.Filled.FilterList),
        IconData("FilterListOff", "Filled", Icons.Filled.FilterListOff),
        IconData("FilterNone", "Filled", Icons.Filled.FilterNone),
        IconData("FilterTiltShift", "Filled", Icons.Filled.FilterTiltShift),
        IconData("FilterVintage", "Filled", Icons.Filled.FilterVintage),
        IconData("FindInPage", "Filled", Icons.Filled.FindInPage),
        IconData("FindReplace", "Filled", Icons.Filled.FindReplace),
        IconData("Fingerprint", "Filled", Icons.Filled.Fingerprint),
        IconData("FireExtinguisher", "Filled", Icons.Filled.FireExtinguisher),
        IconData("FireHydrantAlt", "Filled", Icons.Filled.FireHydrantAlt),
        IconData("FireTruck", "Filled", Icons.Filled.FireTruck),
        IconData("Fireplace", "Filled", Icons.Filled.Fireplace),
        IconData("FirstPage", "Filled", Icons.Filled.FirstPage),
        IconData("FitScreen", "Filled", Icons.Filled.FitScreen),
        IconData("Fitbit", "Filled", Icons.Filled.Fitbit),
        IconData("FitnessCenter", "Filled", Icons.Filled.FitnessCenter),
        IconData("Flag", "Filled", Icons.Filled.Flag),
        IconData("FlagCircle", "Filled", Icons.Filled.FlagCircle),
        IconData("Flaky", "Filled", Icons.Filled.Flaky),
        IconData("Flare", "Filled", Icons.Filled.Flare),
        IconData("FlashAuto", "Filled", Icons.Filled.FlashAuto),
        IconData("FlashOff", "Filled", Icons.Filled.FlashOff),
        IconData("FlashOn", "Filled", Icons.Filled.FlashOn),
        IconData("FlashlightOff", "Filled", Icons.Filled.FlashlightOff),
        IconData("FlashlightOn", "Filled", Icons.Filled.FlashlightOn),
        IconData("Flatware", "Filled", Icons.Filled.Flatware),
        IconData("Flight", "Filled", Icons.Filled.Flight),
        IconData("FlightClass", "Filled", Icons.Filled.FlightClass),
        IconData("FlightLand", "Filled", Icons.Filled.FlightLand),
        IconData("FlightTakeoff", "Filled", Icons.Filled.FlightTakeoff),
        IconData("Flip", "Filled", Icons.Filled.Flip),
        IconData("FlipCameraAndroid", "Filled", Icons.Filled.FlipCameraAndroid),
        IconData("FlipCameraIos", "Filled", Icons.Filled.FlipCameraIos),
        IconData("FlipToBack", "Filled", Icons.Filled.FlipToBack),
        IconData("FlipToFront", "Filled", Icons.Filled.FlipToFront),
        IconData("Flood", "Filled", Icons.Filled.Flood),
        IconData("Flourescent", "Filled", Icons.Filled.Flourescent),
        IconData("Fluorescent", "Filled", Icons.Filled.Fluorescent),
        IconData("FlutterDash", "Filled", Icons.Filled.FlutterDash),
        IconData("FmdBad", "Filled", Icons.Filled.FmdBad),
        IconData("FmdGood", "Filled", Icons.Filled.FmdGood),
        IconData("Folder", "Filled", Icons.Filled.Folder),
        IconData("FolderCopy", "Filled", Icons.Filled.FolderCopy),
        IconData("FolderDelete", "Filled", Icons.Filled.FolderDelete),
        IconData("FolderOff", "Filled", Icons.Filled.FolderOff),
        IconData("FolderOpen", "Filled", Icons.Filled.FolderOpen),
        IconData("FolderShared", "Filled", Icons.Filled.FolderShared),
        IconData("FolderSpecial", "Filled", Icons.Filled.FolderSpecial),
        IconData("FolderZip", "Filled", Icons.Filled.FolderZip),
        IconData("FontDownload", "Filled", Icons.Filled.FontDownload),
        IconData("FontDownloadOff", "Filled", Icons.Filled.FontDownloadOff),
        IconData("FoodBank", "Filled", Icons.Filled.FoodBank),
        IconData("Forest", "Filled", Icons.Filled.Forest),
        IconData("ForkLeft", "Filled", Icons.Filled.ForkLeft),
        IconData("ForkRight", "Filled", Icons.Filled.ForkRight),
        IconData("FormatAlignCenter", "Filled", Icons.Filled.FormatAlignCenter),
        IconData("FormatAlignJustify", "Filled", Icons.Filled.FormatAlignJustify),
        IconData("FormatBold", "Filled", Icons.Filled.FormatBold),
        IconData("FormatClear", "Filled", Icons.Filled.FormatClear),
        IconData("FormatColorFill", "Filled", Icons.Filled.FormatColorFill),
        IconData("FormatColorReset", "Filled", Icons.Filled.FormatColorReset),
        IconData("FormatColorText", "Filled", Icons.Filled.FormatColorText),
        IconData("FormatItalic", "Filled", Icons.Filled.FormatItalic),
        IconData("FormatLineSpacing", "Filled", Icons.Filled.FormatLineSpacing),
        IconData("FormatListNumbered", "Filled", Icons.Filled.FormatListNumbered),
        IconData("FormatListNumberedRtl", "Filled", Icons.Filled.FormatListNumberedRtl),
        IconData("FormatOverline", "Filled", Icons.Filled.FormatOverline),
        IconData("FormatPaint", "Filled", Icons.Filled.FormatPaint),
        IconData("FormatQuote", "Filled", Icons.Filled.FormatQuote),
        IconData("FormatShapes", "Filled", Icons.Filled.FormatShapes),
        IconData("FormatSize", "Filled", Icons.Filled.FormatSize),
        IconData("FormatStrikethrough", "Filled", Icons.Filled.FormatStrikethrough),
        IconData("FormatUnderlined", "Filled", Icons.Filled.FormatUnderlined),
        IconData("Fort", "Filled", Icons.Filled.Fort),
        IconData("Forum", "Filled", Icons.Filled.Forum),
        IconData("Forward10", "Filled", Icons.Filled.Forward10),
        IconData("Forward30", "Filled", Icons.Filled.Forward30),
        IconData("Forward5", "Filled", Icons.Filled.Forward5),
        IconData("Foundation", "Filled", Icons.Filled.Foundation),
        IconData("FreeBreakfast", "Filled", Icons.Filled.FreeBreakfast),
        IconData("FreeCancellation", "Filled", Icons.Filled.FreeCancellation),
        IconData("FrontHand", "Filled", Icons.Filled.FrontHand),
        IconData("Fullscreen", "Filled", Icons.Filled.Fullscreen),
        IconData("FullscreenExit", "Filled", Icons.Filled.FullscreenExit),
        IconData("Functions", "Filled", Icons.Filled.Functions),
        IconData("GMobiledata", "Filled", Icons.Filled.GMobiledata),
        IconData("GTranslate", "Filled", Icons.Filled.GTranslate),
        IconData("Gamepad", "Filled", Icons.Filled.Gamepad),
        IconData("Games", "Filled", Icons.Filled.Games),
        IconData("Garage", "Filled", Icons.Filled.Garage),
        IconData("GasMeter", "Filled", Icons.Filled.GasMeter),
        IconData("Gavel", "Filled", Icons.Filled.Gavel),
        IconData("GeneratingTokens", "Filled", Icons.Filled.GeneratingTokens),
        IconData("Gesture", "Filled", Icons.Filled.Gesture),
        IconData("GetApp", "Filled", Icons.Filled.GetApp),
        IconData("Gif", "Filled", Icons.Filled.Gif),
        IconData("GifBox", "Filled", Icons.Filled.GifBox),
        IconData("Girl", "Filled", Icons.Filled.Girl),
        IconData("Gite", "Filled", Icons.Filled.Gite),
        IconData("GolfCourse", "Filled", Icons.Filled.GolfCourse),
        IconData("GppBad", "Filled", Icons.Filled.GppBad),
        IconData("GppGood", "Filled", Icons.Filled.GppGood),
        IconData("GppMaybe", "Filled", Icons.Filled.GppMaybe),
        IconData("GpsFixed", "Filled", Icons.Filled.GpsFixed),
        IconData("GpsNotFixed", "Filled", Icons.Filled.GpsNotFixed),
        IconData("GpsOff", "Filled", Icons.Filled.GpsOff),
        IconData("Grade", "Filled", Icons.Filled.Grade),
        IconData("Gradient", "Filled", Icons.Filled.Gradient),
        IconData("Grain", "Filled", Icons.Filled.Grain),
        IconData("GraphicEq", "Filled", Icons.Filled.GraphicEq),
        IconData("Grass", "Filled", Icons.Filled.Grass),
        IconData("Grid3x3", "Filled", Icons.Filled.Grid3x3),
        IconData("Grid4x4", "Filled", Icons.Filled.Grid4x4),
        IconData("GridGoldenratio", "Filled", Icons.Filled.GridGoldenratio),
        IconData("GridOff", "Filled", Icons.Filled.GridOff),
        IconData("GridOn", "Filled", Icons.Filled.GridOn),
        IconData("GridView", "Filled", Icons.Filled.GridView),
        IconData("Group", "Filled", Icons.Filled.Group),
        IconData("GroupAdd", "Filled", Icons.Filled.GroupAdd),
        IconData("GroupOff", "Filled", Icons.Filled.GroupOff),
        IconData("GroupRemove", "Filled", Icons.Filled.GroupRemove),
        IconData("GroupWork", "Filled", Icons.Filled.GroupWork),
        IconData("Groups", "Filled", Icons.Filled.Groups),
        IconData("Groups2", "Filled", Icons.Filled.Groups2),
        IconData("Groups3", "Filled", Icons.Filled.Groups3),
        IconData("HMobiledata", "Filled", Icons.Filled.HMobiledata),
        IconData("HPlusMobiledata", "Filled", Icons.Filled.HPlusMobiledata),
        IconData("Hail", "Filled", Icons.Filled.Hail),
        IconData("Handshake", "Filled", Icons.Filled.Handshake),
        IconData("Handyman", "Filled", Icons.Filled.Handyman),
        IconData("Hardware", "Filled", Icons.Filled.Hardware),
        IconData("Hd", "Filled", Icons.Filled.Hd),
        IconData("HdrAuto", "Filled", Icons.Filled.HdrAuto),
        IconData("HdrAutoSelect", "Filled", Icons.Filled.HdrAutoSelect),
        IconData("HdrEnhancedSelect", "Filled", Icons.Filled.HdrEnhancedSelect),
        IconData("HdrOff", "Filled", Icons.Filled.HdrOff),
        IconData("HdrOffSelect", "Filled", Icons.Filled.HdrOffSelect),
        IconData("HdrOn", "Filled", Icons.Filled.HdrOn),
        IconData("HdrOnSelect", "Filled", Icons.Filled.HdrOnSelect),
        IconData("HdrPlus", "Filled", Icons.Filled.HdrPlus),
        IconData("HdrStrong", "Filled", Icons.Filled.HdrStrong),
        IconData("HdrWeak", "Filled", Icons.Filled.HdrWeak),
        IconData("Headphones", "Filled", Icons.Filled.Headphones),
        IconData("HeadphonesBattery", "Filled", Icons.Filled.HeadphonesBattery),
        IconData("Headset", "Filled", Icons.Filled.Headset),
        IconData("HeadsetMic", "Filled", Icons.Filled.HeadsetMic),
        IconData("HeadsetOff", "Filled", Icons.Filled.HeadsetOff),
        IconData("Healing", "Filled", Icons.Filled.Healing),
        IconData("HealthAndSafety", "Filled", Icons.Filled.HealthAndSafety),
        IconData("Hearing", "Filled", Icons.Filled.Hearing),
        IconData("HearingDisabled", "Filled", Icons.Filled.HearingDisabled),
        IconData("HeartBroken", "Filled", Icons.Filled.HeartBroken),
        IconData("HeatPump", "Filled", Icons.Filled.HeatPump),
        IconData("Height", "Filled", Icons.Filled.Height),
        IconData("Hevc", "Filled", Icons.Filled.Hevc),
        IconData("Hexagon", "Filled", Icons.Filled.Hexagon),
        IconData("HideImage", "Filled", Icons.Filled.HideImage),
        IconData("HideSource", "Filled", Icons.Filled.HideSource),
        IconData("HighQuality", "Filled", Icons.Filled.HighQuality),
        IconData("Highlight", "Filled", Icons.Filled.Highlight),
        IconData("HighlightAlt", "Filled", Icons.Filled.HighlightAlt),
        IconData("HighlightOff", "Filled", Icons.Filled.HighlightOff),
        IconData("Hiking", "Filled", Icons.Filled.Hiking),
        IconData("History", "Filled", Icons.Filled.History),
        IconData("HistoryEdu", "Filled", Icons.Filled.HistoryEdu),
        IconData("HistoryToggleOff", "Filled", Icons.Filled.HistoryToggleOff),
        IconData("Hive", "Filled", Icons.Filled.Hive),
        IconData("Hls", "Filled", Icons.Filled.Hls),
        IconData("HlsOff", "Filled", Icons.Filled.HlsOff),
        IconData("HolidayVillage", "Filled", Icons.Filled.HolidayVillage),
        IconData("HomeMax", "Filled", Icons.Filled.HomeMax),
        IconData("HomeMini", "Filled", Icons.Filled.HomeMini),
        IconData("HomeRepairService", "Filled", Icons.Filled.HomeRepairService),
        IconData("HomeWork", "Filled", Icons.Filled.HomeWork),
        IconData("HorizontalDistribute", "Filled", Icons.Filled.HorizontalDistribute),
        IconData("HorizontalRule", "Filled", Icons.Filled.HorizontalRule),
        IconData("HorizontalSplit", "Filled", Icons.Filled.HorizontalSplit),
        IconData("HotTub", "Filled", Icons.Filled.HotTub),
        IconData("Hotel", "Filled", Icons.Filled.Hotel),
        IconData("HotelClass", "Filled", Icons.Filled.HotelClass),
        IconData("HourglassBottom", "Filled", Icons.Filled.HourglassBottom),
        IconData("HourglassDisabled", "Filled", Icons.Filled.HourglassDisabled),
        IconData("HourglassEmpty", "Filled", Icons.Filled.HourglassEmpty),
        IconData("HourglassFull", "Filled", Icons.Filled.HourglassFull),
        IconData("HourglassTop", "Filled", Icons.Filled.HourglassTop),
        IconData("House", "Filled", Icons.Filled.House),
        IconData("HouseSiding", "Filled", Icons.Filled.HouseSiding),
        IconData("Houseboat", "Filled", Icons.Filled.Houseboat),
        IconData("HowToReg", "Filled", Icons.Filled.HowToReg),
        IconData("HowToVote", "Filled", Icons.Filled.HowToVote),
        IconData("Html", "Filled", Icons.Filled.Html),
        IconData("Http", "Filled", Icons.Filled.Http),
        IconData("Https", "Filled", Icons.Filled.Https),
        IconData("Hub", "Filled", Icons.Filled.Hub),
        IconData("Hvac", "Filled", Icons.Filled.Hvac),
        IconData("IceSkating", "Filled", Icons.Filled.IceSkating),
        IconData("Icecream", "Filled", Icons.Filled.Icecream),
        IconData("Image", "Filled", Icons.Filled.Image),
        IconData("ImageAspectRatio", "Filled", Icons.Filled.ImageAspectRatio),
        IconData("ImageNotSupported", "Filled", Icons.Filled.ImageNotSupported),
        IconData("ImageSearch", "Filled", Icons.Filled.ImageSearch),
        IconData("ImagesearchRoller", "Filled", Icons.Filled.ImagesearchRoller),
        IconData("ImportContacts", "Filled", Icons.Filled.ImportContacts),
        IconData("ImportExport", "Filled", Icons.Filled.ImportExport),
        IconData("ImportantDevices", "Filled", Icons.Filled.ImportantDevices),
        IconData("Inbox", "Filled", Icons.Filled.Inbox),
        IconData("IncompleteCircle", "Filled", Icons.Filled.IncompleteCircle),
        IconData("IndeterminateCheckBox", "Filled", Icons.Filled.IndeterminateCheckBox),
        IconData("InsertChart", "Filled", Icons.Filled.InsertChart),
        IconData("InsertChartOutlined", "Filled", Icons.Filled.InsertChartOutlined),
        IconData("InsertEmoticon", "Filled", Icons.Filled.InsertEmoticon),
        IconData("InsertInvitation", "Filled", Icons.Filled.InsertInvitation),
        IconData("InsertLink", "Filled", Icons.Filled.InsertLink),
        IconData("InsertPageBreak", "Filled", Icons.Filled.InsertPageBreak),
        IconData("InsertPhoto", "Filled", Icons.Filled.InsertPhoto),
        IconData("Insights", "Filled", Icons.Filled.Insights),
        IconData("InstallDesktop", "Filled", Icons.Filled.InstallDesktop),
        IconData("InstallMobile", "Filled", Icons.Filled.InstallMobile),
        IconData("IntegrationInstructions", "Filled", Icons.Filled.IntegrationInstructions),
        IconData("Interests", "Filled", Icons.Filled.Interests),
        IconData("InterpreterMode", "Filled", Icons.Filled.InterpreterMode),
        IconData("Inventory", "Filled", Icons.Filled.Inventory),
        IconData("Inventory2", "Filled", Icons.Filled.Inventory2),
        IconData("InvertColors", "Filled", Icons.Filled.InvertColors),
        IconData("InvertColorsOff", "Filled", Icons.Filled.InvertColorsOff),
        IconData("IosShare", "Filled", Icons.Filled.IosShare),
        IconData("Iron", "Filled", Icons.Filled.Iron),
        IconData("Iso", "Filled", Icons.Filled.Iso),
        IconData("Javascript", "Filled", Icons.Filled.Javascript),
        IconData("JoinFull", "Filled", Icons.Filled.JoinFull),
        IconData("JoinInner", "Filled", Icons.Filled.JoinInner),
        IconData("JoinLeft", "Filled", Icons.Filled.JoinLeft),
        IconData("JoinRight", "Filled", Icons.Filled.JoinRight),
        IconData("Kayaking", "Filled", Icons.Filled.Kayaking),
        IconData("KebabDining", "Filled", Icons.Filled.KebabDining),
        IconData("Key", "Filled", Icons.Filled.Key),
        IconData("KeyOff", "Filled", Icons.Filled.KeyOff),
        IconData("Keyboard", "Filled", Icons.Filled.Keyboard),
        IconData("KeyboardAlt", "Filled", Icons.Filled.KeyboardAlt),
        IconData("KeyboardCapslock", "Filled", Icons.Filled.KeyboardCapslock),
        IconData("KeyboardCommandKey", "Filled", Icons.Filled.KeyboardCommandKey),
        IconData("KeyboardControlKey", "Filled", Icons.Filled.KeyboardControlKey),
        IconData("KeyboardDoubleArrowDown", "Filled", Icons.Filled.KeyboardDoubleArrowDown),
        IconData("KeyboardDoubleArrowLeft", "Filled", Icons.Filled.KeyboardDoubleArrowLeft),
        IconData("KeyboardDoubleArrowRight", "Filled", Icons.Filled.KeyboardDoubleArrowRight),
        IconData("KeyboardDoubleArrowUp", "Filled", Icons.Filled.KeyboardDoubleArrowUp),
        IconData("KeyboardHide", "Filled", Icons.Filled.KeyboardHide),
        IconData("KeyboardOptionKey", "Filled", Icons.Filled.KeyboardOptionKey),
        IconData("KeyboardVoice", "Filled", Icons.Filled.KeyboardVoice),
        IconData("KingBed", "Filled", Icons.Filled.KingBed),
        IconData("Kitchen", "Filled", Icons.Filled.Kitchen),
        IconData("Kitesurfing", "Filled", Icons.Filled.Kitesurfing),
        IconData("Lan", "Filled", Icons.Filled.Lan),
        IconData("Landscape", "Filled", Icons.Filled.Landscape),
        IconData("Landslide", "Filled", Icons.Filled.Landslide),
        IconData("Language", "Filled", Icons.Filled.Language),
        IconData("Laptop", "Filled", Icons.Filled.Laptop),
        IconData("LaptopChromebook", "Filled", Icons.Filled.LaptopChromebook),
        IconData("LaptopMac", "Filled", Icons.Filled.LaptopMac),
        IconData("LaptopWindows", "Filled", Icons.Filled.LaptopWindows),
        IconData("Layers", "Filled", Icons.Filled.Layers),
        IconData("LayersClear", "Filled", Icons.Filled.LayersClear),
        IconData("Leaderboard", "Filled", Icons.Filled.Leaderboard),
        IconData("LeakAdd", "Filled", Icons.Filled.LeakAdd),
        IconData("LeakRemove", "Filled", Icons.Filled.LeakRemove),
        IconData("LeaveBagsAtHome", "Filled", Icons.Filled.LeaveBagsAtHome),
        IconData("LegendToggle", "Filled", Icons.Filled.LegendToggle),
        IconData("Lens", "Filled", Icons.Filled.Lens),
        IconData("LensBlur", "Filled", Icons.Filled.LensBlur),
        IconData("LibraryAdd", "Filled", Icons.Filled.LibraryAdd),
        IconData("LibraryAddCheck", "Filled", Icons.Filled.LibraryAddCheck),
        IconData("LibraryMusic", "Filled", Icons.Filled.LibraryMusic),
        IconData("Light", "Filled", Icons.Filled.Light),
        IconData("LightMode", "Filled", Icons.Filled.LightMode),
        IconData("Lightbulb", "Filled", Icons.Filled.Lightbulb),
        IconData("LightbulbCircle", "Filled", Icons.Filled.LightbulbCircle),
        IconData("LineAxis", "Filled", Icons.Filled.LineAxis),
        IconData("LineStyle", "Filled", Icons.Filled.LineStyle),
        IconData("LineWeight", "Filled", Icons.Filled.LineWeight),
        IconData("LinearScale", "Filled", Icons.Filled.LinearScale),
        IconData("Link", "Filled", Icons.Filled.Link),
        IconData("LinkOff", "Filled", Icons.Filled.LinkOff),
        IconData("LinkedCamera", "Filled", Icons.Filled.LinkedCamera),
        IconData("Liquor", "Filled", Icons.Filled.Liquor),
        IconData("LiveTv", "Filled", Icons.Filled.LiveTv),
        IconData("Living", "Filled", Icons.Filled.Living),
        IconData("LocalActivity", "Filled", Icons.Filled.LocalActivity),
        IconData("LocalAirport", "Filled", Icons.Filled.LocalAirport),
        IconData("LocalAtm", "Filled", Icons.Filled.LocalAtm),
        IconData("LocalBar", "Filled", Icons.Filled.LocalBar),
        IconData("LocalCafe", "Filled", Icons.Filled.LocalCafe),
        IconData("LocalCarWash", "Filled", Icons.Filled.LocalCarWash),
        IconData("LocalConvenienceStore", "Filled", Icons.Filled.LocalConvenienceStore),
        IconData("LocalDining", "Filled", Icons.Filled.LocalDining),
        IconData("LocalDrink", "Filled", Icons.Filled.LocalDrink),
        IconData("LocalFireDepartment", "Filled", Icons.Filled.LocalFireDepartment),
        IconData("LocalFlorist", "Filled", Icons.Filled.LocalFlorist),
        IconData("LocalGasStation", "Filled", Icons.Filled.LocalGasStation),
        IconData("LocalGroceryStore", "Filled", Icons.Filled.LocalGroceryStore),
        IconData("LocalHospital", "Filled", Icons.Filled.LocalHospital),
        IconData("LocalHotel", "Filled", Icons.Filled.LocalHotel),
        IconData("LocalLaundryService", "Filled", Icons.Filled.LocalLaundryService),
        IconData("LocalLibrary", "Filled", Icons.Filled.LocalLibrary),
        IconData("LocalMall", "Filled", Icons.Filled.LocalMall),
        IconData("LocalMovies", "Filled", Icons.Filled.LocalMovies),
        IconData("LocalOffer", "Filled", Icons.Filled.LocalOffer),
        IconData("LocalParking", "Filled", Icons.Filled.LocalParking),
        IconData("LocalPharmacy", "Filled", Icons.Filled.LocalPharmacy),
        IconData("LocalPhone", "Filled", Icons.Filled.LocalPhone),
        IconData("LocalPizza", "Filled", Icons.Filled.LocalPizza),
        IconData("LocalPlay", "Filled", Icons.Filled.LocalPlay),
        IconData("LocalPolice", "Filled", Icons.Filled.LocalPolice),
        IconData("LocalPostOffice", "Filled", Icons.Filled.LocalPostOffice),
        IconData("LocalPrintshop", "Filled", Icons.Filled.LocalPrintshop),
        IconData("LocalSee", "Filled", Icons.Filled.LocalSee),
        IconData("LocalShipping", "Filled", Icons.Filled.LocalShipping),
        IconData("LocalTaxi", "Filled", Icons.Filled.LocalTaxi),
        IconData("LocationCity", "Filled", Icons.Filled.LocationCity),
        IconData("LocationDisabled", "Filled", Icons.Filled.LocationDisabled),
        IconData("LocationOff", "Filled", Icons.Filled.LocationOff),
        IconData("LocationSearching", "Filled", Icons.Filled.LocationSearching),
        IconData("LockClock", "Filled", Icons.Filled.LockClock),
        IconData("LockOpen", "Filled", Icons.Filled.LockOpen),
        IconData("LockPerson", "Filled", Icons.Filled.LockPerson),
        IconData("LockReset", "Filled", Icons.Filled.LockReset),
        IconData("LogoDev", "Filled", Icons.Filled.LogoDev),
        IconData("Looks", "Filled", Icons.Filled.Looks),
        IconData("Looks3", "Filled", Icons.Filled.Looks3),
        IconData("Looks4", "Filled", Icons.Filled.Looks4),
        IconData("Looks5", "Filled", Icons.Filled.Looks5),
        IconData("Looks6", "Filled", Icons.Filled.Looks6),
        IconData("LooksOne", "Filled", Icons.Filled.LooksOne),
        IconData("LooksTwo", "Filled", Icons.Filled.LooksTwo),
        IconData("Loop", "Filled", Icons.Filled.Loop),
        IconData("Loupe", "Filled", Icons.Filled.Loupe),
        IconData("LowPriority", "Filled", Icons.Filled.LowPriority),
        IconData("Loyalty", "Filled", Icons.Filled.Loyalty),
        IconData("LteMobiledata", "Filled", Icons.Filled.LteMobiledata),
        IconData("LtePlusMobiledata", "Filled", Icons.Filled.LtePlusMobiledata),
        IconData("Luggage", "Filled", Icons.Filled.Luggage),
        IconData("LunchDining", "Filled", Icons.Filled.LunchDining),
        IconData("Lyrics", "Filled", Icons.Filled.Lyrics),
        IconData("MacroOff", "Filled", Icons.Filled.MacroOff),
        IconData("Mail", "Filled", Icons.Filled.Mail),
        IconData("MailLock", "Filled", Icons.Filled.MailLock),
        IconData("Male", "Filled", Icons.Filled.Male),
        IconData("Man", "Filled", Icons.Filled.Man),
        IconData("Man2", "Filled", Icons.Filled.Man2),
        IconData("Man3", "Filled", Icons.Filled.Man3),
        IconData("Man4", "Filled", Icons.Filled.Man4),
        IconData("ManageAccounts", "Filled", Icons.Filled.ManageAccounts),
        IconData("ManageHistory", "Filled", Icons.Filled.ManageHistory),
        IconData("Map", "Filled", Icons.Filled.Map),
        IconData("MapsHomeWork", "Filled", Icons.Filled.MapsHomeWork),
        IconData("MapsUgc", "Filled", Icons.Filled.MapsUgc),
        IconData("Margin", "Filled", Icons.Filled.Margin),
        IconData("MarkAsUnread", "Filled", Icons.Filled.MarkAsUnread),
        IconData("MarkChatRead", "Filled", Icons.Filled.MarkChatRead),
        IconData("MarkChatUnread", "Filled", Icons.Filled.MarkChatUnread),
        IconData("MarkEmailRead", "Filled", Icons.Filled.MarkEmailRead),
        IconData("MarkEmailUnread", "Filled", Icons.Filled.MarkEmailUnread),
        IconData("MarkUnreadChatAlt", "Filled", Icons.Filled.MarkUnreadChatAlt),
        IconData("Markunread", "Filled", Icons.Filled.Markunread),
        IconData("MarkunreadMailbox", "Filled", Icons.Filled.MarkunreadMailbox),
        IconData("Masks", "Filled", Icons.Filled.Masks),
        IconData("Maximize", "Filled", Icons.Filled.Maximize),
        IconData("MediaBluetoothOff", "Filled", Icons.Filled.MediaBluetoothOff),
        IconData("MediaBluetoothOn", "Filled", Icons.Filled.MediaBluetoothOn),
        IconData("Mediation", "Filled", Icons.Filled.Mediation),
        IconData("MedicalInformation", "Filled", Icons.Filled.MedicalInformation),
        IconData("MedicalServices", "Filled", Icons.Filled.MedicalServices),
        IconData("Medication", "Filled", Icons.Filled.Medication),
        IconData("MeetingRoom", "Filled", Icons.Filled.MeetingRoom),
        IconData("Memory", "Filled", Icons.Filled.Memory),
        IconData("Merge", "Filled", Icons.Filled.Merge),
        IconData("Mic", "Filled", Icons.Filled.Mic),
        IconData("MicExternalOff", "Filled", Icons.Filled.MicExternalOff),
        IconData("MicExternalOn", "Filled", Icons.Filled.MicExternalOn),
        IconData("MicNone", "Filled", Icons.Filled.MicNone),
        IconData("MicOff", "Filled", Icons.Filled.MicOff),
        IconData("Microwave", "Filled", Icons.Filled.Microwave),
        IconData("MilitaryTech", "Filled", Icons.Filled.MilitaryTech),
        IconData("Minimize", "Filled", Icons.Filled.Minimize),
        IconData("MinorCrash", "Filled", Icons.Filled.MinorCrash),
        IconData("MiscellaneousServices", "Filled", Icons.Filled.MiscellaneousServices),
        IconData("Mms", "Filled", Icons.Filled.Mms),
        IconData("MobileFriendly", "Filled", Icons.Filled.MobileFriendly),
        IconData("MobileOff", "Filled", Icons.Filled.MobileOff),
        IconData("MobiledataOff", "Filled", Icons.Filled.MobiledataOff),
        IconData("Mode", "Filled", Icons.Filled.Mode),
        IconData("ModeComment", "Filled", Icons.Filled.ModeComment),
        IconData("ModeEdit", "Filled", Icons.Filled.ModeEdit),
        IconData("ModeEditOutline", "Filled", Icons.Filled.ModeEditOutline),
        IconData("ModeFanOff", "Filled", Icons.Filled.ModeFanOff),
        IconData("ModeNight", "Filled", Icons.Filled.ModeNight),
        IconData("ModeOfTravel", "Filled", Icons.Filled.ModeOfTravel),
        IconData("ModeStandby", "Filled", Icons.Filled.ModeStandby),
        IconData("ModelTraining", "Filled", Icons.Filled.ModelTraining),
        IconData("MonetizationOn", "Filled", Icons.Filled.MonetizationOn),
        IconData("Money", "Filled", Icons.Filled.Money),
        IconData("MoneyOff", "Filled", Icons.Filled.MoneyOff),
        IconData("MoneyOffCsred", "Filled", Icons.Filled.MoneyOffCsred),
        IconData("Monitor", "Filled", Icons.Filled.Monitor),
        IconData("MonitorHeart", "Filled", Icons.Filled.MonitorHeart),
        IconData("MonitorWeight", "Filled", Icons.Filled.MonitorWeight),
        IconData("MonochromePhotos", "Filled", Icons.Filled.MonochromePhotos),
        IconData("Mood", "Filled", Icons.Filled.Mood),
        IconData("MoodBad", "Filled", Icons.Filled.MoodBad),
        IconData("Moped", "Filled", Icons.Filled.Moped),
        IconData("MoreHoriz", "Filled", Icons.Filled.MoreHoriz),
        IconData("MoreTime", "Filled", Icons.Filled.MoreTime),
        IconData("Mosque", "Filled", Icons.Filled.Mosque),
        IconData("MotionPhotosAuto", "Filled", Icons.Filled.MotionPhotosAuto),
        IconData("MotionPhotosOff", "Filled", Icons.Filled.MotionPhotosOff),
        IconData("MotionPhotosOn", "Filled", Icons.Filled.MotionPhotosOn),
        IconData("MotionPhotosPause", "Filled", Icons.Filled.MotionPhotosPause),
        IconData("MotionPhotosPaused", "Filled", Icons.Filled.MotionPhotosPaused),
        IconData("Motorcycle", "Filled", Icons.Filled.Motorcycle),
        IconData("Mouse", "Filled", Icons.Filled.Mouse),
        IconData("MoveDown", "Filled", Icons.Filled.MoveDown),
        IconData("MoveToInbox", "Filled", Icons.Filled.MoveToInbox),
        IconData("MoveUp", "Filled", Icons.Filled.MoveUp),
        IconData("Movie", "Filled", Icons.Filled.Movie),
        IconData("MovieCreation", "Filled", Icons.Filled.MovieCreation),
        IconData("MovieFilter", "Filled", Icons.Filled.MovieFilter),
        IconData("Moving", "Filled", Icons.Filled.Moving),
        IconData("Mp", "Filled", Icons.Filled.Mp),
        IconData("MultipleStop", "Filled", Icons.Filled.MultipleStop),
        IconData("Museum", "Filled", Icons.Filled.Museum),
        IconData("MusicNote", "Filled", Icons.Filled.MusicNote),
        IconData("MusicOff", "Filled", Icons.Filled.MusicOff),
        IconData("MusicVideo", "Filled", Icons.Filled.MusicVideo),
        IconData("MyLocation", "Filled", Icons.Filled.MyLocation),
        IconData("Nat", "Filled", Icons.Filled.Nat),
        IconData("Nature", "Filled", Icons.Filled.Nature),
        IconData("NaturePeople", "Filled", Icons.Filled.NaturePeople),
        IconData("Navigation", "Filled", Icons.Filled.Navigation),
        IconData("NearMe", "Filled", Icons.Filled.NearMe),
        IconData("NearMeDisabled", "Filled", Icons.Filled.NearMeDisabled),
        IconData("NearbyError", "Filled", Icons.Filled.NearbyError),
        IconData("NearbyOff", "Filled", Icons.Filled.NearbyOff),
        IconData("NestCamWiredStand", "Filled", Icons.Filled.NestCamWiredStand),
        IconData("NetworkCell", "Filled", Icons.Filled.NetworkCell),
        IconData("NetworkCheck", "Filled", Icons.Filled.NetworkCheck),
        IconData("NetworkLocked", "Filled", Icons.Filled.NetworkLocked),
        IconData("NetworkPing", "Filled", Icons.Filled.NetworkPing),
        IconData("NetworkWifi", "Filled", Icons.Filled.NetworkWifi),
        IconData("NetworkWifi1Bar", "Filled", Icons.Filled.NetworkWifi1Bar),
        IconData("NetworkWifi2Bar", "Filled", Icons.Filled.NetworkWifi2Bar),
        IconData("NetworkWifi3Bar", "Filled", Icons.Filled.NetworkWifi3Bar),
        IconData("NewLabel", "Filled", Icons.Filled.NewLabel),
        IconData("NewReleases", "Filled", Icons.Filled.NewReleases),
        IconData("Newspaper", "Filled", Icons.Filled.Newspaper),
        IconData("Nfc", "Filled", Icons.Filled.Nfc),
        IconData("NightShelter", "Filled", Icons.Filled.NightShelter),
        IconData("Nightlife", "Filled", Icons.Filled.Nightlife),
        IconData("Nightlight", "Filled", Icons.Filled.Nightlight),
        IconData("NightlightRound", "Filled", Icons.Filled.NightlightRound),
        IconData("NightsStay", "Filled", Icons.Filled.NightsStay),
        IconData("NoAccounts", "Filled", Icons.Filled.NoAccounts),
        IconData("NoAdultContent", "Filled", Icons.Filled.NoAdultContent),
        IconData("NoBackpack", "Filled", Icons.Filled.NoBackpack),
        IconData("NoCell", "Filled", Icons.Filled.NoCell),
        IconData("NoCrash", "Filled", Icons.Filled.NoCrash),
        IconData("NoDrinks", "Filled", Icons.Filled.NoDrinks),
        IconData("NoEncryption", "Filled", Icons.Filled.NoEncryption),
        IconData("NoEncryptionGmailerrorred", "Filled", Icons.Filled.NoEncryptionGmailerrorred),
        IconData("NoFlash", "Filled", Icons.Filled.NoFlash),
        IconData("NoFood", "Filled", Icons.Filled.NoFood),
        IconData("NoLuggage", "Filled", Icons.Filled.NoLuggage),
        IconData("NoMeals", "Filled", Icons.Filled.NoMeals),
        IconData("NoMeetingRoom", "Filled", Icons.Filled.NoMeetingRoom),
        IconData("NoPhotography", "Filled", Icons.Filled.NoPhotography),
        IconData("NoSim", "Filled", Icons.Filled.NoSim),
        IconData("NoStroller", "Filled", Icons.Filled.NoStroller),
        IconData("NoTransfer", "Filled", Icons.Filled.NoTransfer),
        IconData("NoiseAware", "Filled", Icons.Filled.NoiseAware),
        IconData("NoiseControlOff", "Filled", Icons.Filled.NoiseControlOff),
        IconData("NordicWalking", "Filled", Icons.Filled.NordicWalking),
        IconData("North", "Filled", Icons.Filled.North),
        IconData("NorthEast", "Filled", Icons.Filled.NorthEast),
        IconData("NorthWest", "Filled", Icons.Filled.NorthWest),
        IconData("NotAccessible", "Filled", Icons.Filled.NotAccessible),
        IconData("NotInterested", "Filled", Icons.Filled.NotInterested),
        IconData("NotStarted", "Filled", Icons.Filled.NotStarted),
        IconData("NoteAlt", "Filled", Icons.Filled.NoteAlt),
        IconData("NotificationAdd", "Filled", Icons.Filled.NotificationAdd),
        IconData("NotificationImportant", "Filled", Icons.Filled.NotificationImportant),
        IconData("NotificationsActive", "Filled", Icons.Filled.NotificationsActive),
        IconData("NotificationsNone", "Filled", Icons.Filled.NotificationsNone),
        IconData("NotificationsOff", "Filled", Icons.Filled.NotificationsOff),
        IconData("NotificationsPaused", "Filled", Icons.Filled.NotificationsPaused),
        IconData("Numbers", "Filled", Icons.Filled.Numbers),
        IconData("OfflineBolt", "Filled", Icons.Filled.OfflineBolt),
        IconData("OfflinePin", "Filled", Icons.Filled.OfflinePin),
        IconData("OilBarrel", "Filled", Icons.Filled.OilBarrel),
        IconData("OnDeviceTraining", "Filled", Icons.Filled.OnDeviceTraining),
        IconData("OndemandVideo", "Filled", Icons.Filled.OndemandVideo),
        IconData("OnlinePrediction", "Filled", Icons.Filled.OnlinePrediction),
        IconData("Opacity", "Filled", Icons.Filled.Opacity),
        IconData("OpenInBrowser", "Filled", Icons.Filled.OpenInBrowser),
        IconData("OpenInFull", "Filled", Icons.Filled.OpenInFull),
        IconData("OpenInNewOff", "Filled", Icons.Filled.OpenInNewOff),
        IconData("OpenWith", "Filled", Icons.Filled.OpenWith),
        IconData("OtherHouses", "Filled", Icons.Filled.OtherHouses),
        IconData("Outbond", "Filled", Icons.Filled.Outbond),
        IconData("Outbox", "Filled", Icons.Filled.Outbox),
        IconData("OutdoorGrill", "Filled", Icons.Filled.OutdoorGrill),
        IconData("Outlet", "Filled", Icons.Filled.Outlet),
        IconData("OutlinedFlag", "Filled", Icons.Filled.OutlinedFlag),
        IconData("Output", "Filled", Icons.Filled.Output),
        IconData("Padding", "Filled", Icons.Filled.Padding),
        IconData("Pages", "Filled", Icons.Filled.Pages),
        IconData("Pageview", "Filled", Icons.Filled.Pageview),
        IconData("Paid", "Filled", Icons.Filled.Paid),
        IconData("Palette", "Filled", Icons.Filled.Palette),
        IconData("PanTool", "Filled", Icons.Filled.PanTool),
        IconData("PanToolAlt", "Filled", Icons.Filled.PanToolAlt),
        IconData("Panorama", "Filled", Icons.Filled.Panorama),
        IconData("PanoramaFishEye", "Filled", Icons.Filled.PanoramaFishEye),
        IconData("PanoramaHorizontal", "Filled", Icons.Filled.PanoramaHorizontal),
        IconData("PanoramaHorizontalSelect", "Filled", Icons.Filled.PanoramaHorizontalSelect),
        IconData("PanoramaPhotosphere", "Filled", Icons.Filled.PanoramaPhotosphere),
        IconData("PanoramaPhotosphereSelect", "Filled", Icons.Filled.PanoramaPhotosphereSelect),
        IconData("PanoramaVertical", "Filled", Icons.Filled.PanoramaVertical),
        IconData("PanoramaVerticalSelect", "Filled", Icons.Filled.PanoramaVerticalSelect),
        IconData("PanoramaWideAngle", "Filled", Icons.Filled.PanoramaWideAngle),
        IconData("PanoramaWideAngleSelect", "Filled", Icons.Filled.PanoramaWideAngleSelect),
        IconData("Paragliding", "Filled", Icons.Filled.Paragliding),
        IconData("Park", "Filled", Icons.Filled.Park),
        IconData("PartyMode", "Filled", Icons.Filled.PartyMode),
        IconData("Password", "Filled", Icons.Filled.Password),
        IconData("Pattern", "Filled", Icons.Filled.Pattern),
        IconData("Pause", "Filled", Icons.Filled.Pause),
        IconData("PauseCircle", "Filled", Icons.Filled.PauseCircle),
        IconData("PauseCircleFilled", "Filled", Icons.Filled.PauseCircleFilled),
        IconData("PauseCircleOutline", "Filled", Icons.Filled.PauseCircleOutline),
        IconData("PausePresentation", "Filled", Icons.Filled.PausePresentation),
        IconData("Payment", "Filled", Icons.Filled.Payment),
        IconData("Payments", "Filled", Icons.Filled.Payments),
        IconData("PedalBike", "Filled", Icons.Filled.PedalBike),
        IconData("Pending", "Filled", Icons.Filled.Pending),
        IconData("PendingActions", "Filled", Icons.Filled.PendingActions),
        IconData("Pentagon", "Filled", Icons.Filled.Pentagon),
        IconData("People", "Filled", Icons.Filled.People),
        IconData("PeopleAlt", "Filled", Icons.Filled.PeopleAlt),
        IconData("PeopleOutline", "Filled", Icons.Filled.PeopleOutline),
        IconData("Percent", "Filled", Icons.Filled.Percent),
        IconData("PermCameraMic", "Filled", Icons.Filled.PermCameraMic),
        IconData("PermContactCalendar", "Filled", Icons.Filled.PermContactCalendar),
        IconData("PermDataSetting", "Filled", Icons.Filled.PermDataSetting),
        IconData("PermDeviceInformation", "Filled", Icons.Filled.PermDeviceInformation),
        IconData("PermIdentity", "Filled", Icons.Filled.PermIdentity),
        IconData("PermMedia", "Filled", Icons.Filled.PermMedia),
        IconData("PermPhoneMsg", "Filled", Icons.Filled.PermPhoneMsg),
        IconData("PermScanWifi", "Filled", Icons.Filled.PermScanWifi),
        IconData("Person2", "Filled", Icons.Filled.Person2),
        IconData("Person3", "Filled", Icons.Filled.Person3),
        IconData("Person4", "Filled", Icons.Filled.Person4),
        IconData("PersonAdd", "Filled", Icons.Filled.PersonAdd),
        IconData("PersonAddAlt", "Filled", Icons.Filled.PersonAddAlt),
        IconData("PersonAddAlt1", "Filled", Icons.Filled.PersonAddAlt1),
        IconData("PersonAddDisabled", "Filled", Icons.Filled.PersonAddDisabled),
        IconData("PersonOff", "Filled", Icons.Filled.PersonOff),
        IconData("PersonOutline", "Filled", Icons.Filled.PersonOutline),
        IconData("PersonPin", "Filled", Icons.Filled.PersonPin),
        IconData("PersonPinCircle", "Filled", Icons.Filled.PersonPinCircle),
        IconData("PersonRemove", "Filled", Icons.Filled.PersonRemove),
        IconData("PersonRemoveAlt1", "Filled", Icons.Filled.PersonRemoveAlt1),
        IconData("PersonSearch", "Filled", Icons.Filled.PersonSearch),
        IconData("PersonalInjury", "Filled", Icons.Filled.PersonalInjury),
        IconData("PersonalVideo", "Filled", Icons.Filled.PersonalVideo),
        IconData("PestControl", "Filled", Icons.Filled.PestControl),
        IconData("PestControlRodent", "Filled", Icons.Filled.PestControlRodent),
        IconData("Pets", "Filled", Icons.Filled.Pets),
        IconData("Phishing", "Filled", Icons.Filled.Phishing),
        IconData("PhoneAndroid", "Filled", Icons.Filled.PhoneAndroid),
        IconData("PhoneBluetoothSpeaker", "Filled", Icons.Filled.PhoneBluetoothSpeaker),
        IconData("PhoneDisabled", "Filled", Icons.Filled.PhoneDisabled),
        IconData("PhoneEnabled", "Filled", Icons.Filled.PhoneEnabled),
        IconData("PhoneInTalk", "Filled", Icons.Filled.PhoneInTalk),
        IconData("PhoneIphone", "Filled", Icons.Filled.PhoneIphone),
        IconData("PhoneLocked", "Filled", Icons.Filled.PhoneLocked),
        IconData("PhonePaused", "Filled", Icons.Filled.PhonePaused),
        IconData("Phonelink", "Filled", Icons.Filled.Phonelink),
        IconData("PhonelinkErase", "Filled", Icons.Filled.PhonelinkErase),
        IconData("PhonelinkLock", "Filled", Icons.Filled.PhonelinkLock),
        IconData("PhonelinkOff", "Filled", Icons.Filled.PhonelinkOff),
        IconData("PhonelinkRing", "Filled", Icons.Filled.PhonelinkRing),
        IconData("PhonelinkSetup", "Filled", Icons.Filled.PhonelinkSetup),
        IconData("Photo", "Filled", Icons.Filled.Photo),
        IconData("PhotoAlbum", "Filled", Icons.Filled.PhotoAlbum),
        IconData("PhotoCamera", "Filled", Icons.Filled.PhotoCamera),
        IconData("PhotoCameraBack", "Filled", Icons.Filled.PhotoCameraBack),
        IconData("PhotoCameraFront", "Filled", Icons.Filled.PhotoCameraFront),
        IconData("PhotoFilter", "Filled", Icons.Filled.PhotoFilter),
        IconData("PhotoLibrary", "Filled", Icons.Filled.PhotoLibrary),
        IconData("PhotoSizeSelectActual", "Filled", Icons.Filled.PhotoSizeSelectActual),
        IconData("PhotoSizeSelectLarge", "Filled", Icons.Filled.PhotoSizeSelectLarge),
        IconData("PhotoSizeSelectSmall", "Filled", Icons.Filled.PhotoSizeSelectSmall),
        IconData("Php", "Filled", Icons.Filled.Php),
        IconData("Piano", "Filled", Icons.Filled.Piano),
        IconData("PianoOff", "Filled", Icons.Filled.PianoOff),
        IconData("PictureAsPdf", "Filled", Icons.Filled.PictureAsPdf),
        IconData("PictureInPicture", "Filled", Icons.Filled.PictureInPicture),
        IconData("PictureInPictureAlt", "Filled", Icons.Filled.PictureInPictureAlt),
        IconData("PieChart", "Filled", Icons.Filled.PieChart),
        IconData("PieChartOutline", "Filled", Icons.Filled.PieChartOutline),
        IconData("Pin", "Filled", Icons.Filled.Pin),
        IconData("PinDrop", "Filled", Icons.Filled.PinDrop),
        IconData("PinEnd", "Filled", Icons.Filled.PinEnd),
        IconData("PinInvoke", "Filled", Icons.Filled.PinInvoke),
        IconData("Pinch", "Filled", Icons.Filled.Pinch),
        IconData("PivotTableChart", "Filled", Icons.Filled.PivotTableChart),
        IconData("Pix", "Filled", Icons.Filled.Pix),
        IconData("Plagiarism", "Filled", Icons.Filled.Plagiarism),
        IconData("PlayCircle", "Filled", Icons.Filled.PlayCircle),
        IconData("PlayCircleFilled", "Filled", Icons.Filled.PlayCircleFilled),
        IconData("PlayCircleOutline", "Filled", Icons.Filled.PlayCircleOutline),
        IconData("PlayDisabled", "Filled", Icons.Filled.PlayDisabled),
        IconData("PlayForWork", "Filled", Icons.Filled.PlayForWork),
        IconData("PlayLesson", "Filled", Icons.Filled.PlayLesson),
        IconData("PlaylistAddCheckCircle", "Filled", Icons.Filled.PlaylistAddCheckCircle),
        IconData("PlaylistAddCircle", "Filled", Icons.Filled.PlaylistAddCircle),
        IconData("PlaylistRemove", "Filled", Icons.Filled.PlaylistRemove),
        IconData("Plumbing", "Filled", Icons.Filled.Plumbing),
        IconData("PlusOne", "Filled", Icons.Filled.PlusOne),
        IconData("Podcasts", "Filled", Icons.Filled.Podcasts),
        IconData("PointOfSale", "Filled", Icons.Filled.PointOfSale),
        IconData("Policy", "Filled", Icons.Filled.Policy),
        IconData("Poll", "Filled", Icons.Filled.Poll),
        IconData("Polyline", "Filled", Icons.Filled.Polyline),
        IconData("Polymer", "Filled", Icons.Filled.Polymer),
        IconData("Pool", "Filled", Icons.Filled.Pool),
        IconData("PortableWifiOff", "Filled", Icons.Filled.PortableWifiOff),
        IconData("Portrait", "Filled", Icons.Filled.Portrait),
        IconData("PostAdd", "Filled", Icons.Filled.PostAdd),
        IconData("Power", "Filled", Icons.Filled.Power),
        IconData("PowerInput", "Filled", Icons.Filled.PowerInput),
        IconData("PowerOff", "Filled", Icons.Filled.PowerOff),
        IconData("PowerSettingsNew", "Filled", Icons.Filled.PowerSettingsNew),
        IconData("PrecisionManufacturing", "Filled", Icons.Filled.PrecisionManufacturing),
        IconData("PregnantWoman", "Filled", Icons.Filled.PregnantWoman),
        IconData("PresentToAll", "Filled", Icons.Filled.PresentToAll),
        IconData("Preview", "Filled", Icons.Filled.Preview),
        IconData("PriceChange", "Filled", Icons.Filled.PriceChange),
        IconData("PriceCheck", "Filled", Icons.Filled.PriceCheck),
        IconData("Print", "Filled", Icons.Filled.Print),
        IconData("PrintDisabled", "Filled", Icons.Filled.PrintDisabled),
        IconData("PriorityHigh", "Filled", Icons.Filled.PriorityHigh),
        IconData("PrivacyTip", "Filled", Icons.Filled.PrivacyTip),
        IconData("PrivateConnectivity", "Filled", Icons.Filled.PrivateConnectivity),
        IconData("ProductionQuantityLimits", "Filled", Icons.Filled.ProductionQuantityLimits),
        IconData("Propane", "Filled", Icons.Filled.Propane),
        IconData("PropaneTank", "Filled", Icons.Filled.PropaneTank),
        IconData("Psychology", "Filled", Icons.Filled.Psychology),
        IconData("PsychologyAlt", "Filled", Icons.Filled.PsychologyAlt),
        IconData("Public", "Filled", Icons.Filled.Public),
        IconData("PublicOff", "Filled", Icons.Filled.PublicOff),
        IconData("Publish", "Filled", Icons.Filled.Publish),
        IconData("PublishedWithChanges", "Filled", Icons.Filled.PublishedWithChanges),
        IconData("PunchClock", "Filled", Icons.Filled.PunchClock),
        IconData("PushPin", "Filled", Icons.Filled.PushPin),
        IconData("QrCode", "Filled", Icons.Filled.QrCode),
        IconData("QrCode2", "Filled", Icons.Filled.QrCode2),
        IconData("QrCodeScanner", "Filled", Icons.Filled.QrCodeScanner),
        IconData("QueryBuilder", "Filled", Icons.Filled.QueryBuilder),
        IconData("QueryStats", "Filled", Icons.Filled.QueryStats),
        IconData("QuestionAnswer", "Filled", Icons.Filled.QuestionAnswer),
        IconData("QuestionMark", "Filled", Icons.Filled.QuestionMark),
        IconData("Queue", "Filled", Icons.Filled.Queue),
        IconData("QueuePlayNext", "Filled", Icons.Filled.QueuePlayNext),
        IconData("Quickreply", "Filled", Icons.Filled.Quickreply),
        IconData("Quiz", "Filled", Icons.Filled.Quiz),
        IconData("RMobiledata", "Filled", Icons.Filled.RMobiledata),
        IconData("Radar", "Filled", Icons.Filled.Radar),
        IconData("Radio", "Filled", Icons.Filled.Radio),
        IconData("RadioButtonChecked", "Filled", Icons.Filled.RadioButtonChecked),
        IconData("RadioButtonUnchecked", "Filled", Icons.Filled.RadioButtonUnchecked),
        IconData("RailwayAlert", "Filled", Icons.Filled.RailwayAlert),
        IconData("RamenDining", "Filled", Icons.Filled.RamenDining),
        IconData("RampLeft", "Filled", Icons.Filled.RampLeft),
        IconData("RampRight", "Filled", Icons.Filled.RampRight),
        IconData("RateReview", "Filled", Icons.Filled.RateReview),
        IconData("RawOff", "Filled", Icons.Filled.RawOff),
        IconData("RawOn", "Filled", Icons.Filled.RawOn),
        IconData("RealEstateAgent", "Filled", Icons.Filled.RealEstateAgent),
        IconData("Receipt", "Filled", Icons.Filled.Receipt),
        IconData("RecentActors", "Filled", Icons.Filled.RecentActors),
        IconData("Recommend", "Filled", Icons.Filled.Recommend),
        IconData("RecordVoiceOver", "Filled", Icons.Filled.RecordVoiceOver),
        IconData("Rectangle", "Filled", Icons.Filled.Rectangle),
        IconData("Recycling", "Filled", Icons.Filled.Recycling),
        IconData("Redeem", "Filled", Icons.Filled.Redeem),
        IconData("ReduceCapacity", "Filled", Icons.Filled.ReduceCapacity),
        IconData("RememberMe", "Filled", Icons.Filled.RememberMe),
        IconData("Remove", "Filled", Icons.Filled.Remove),
        IconData("RemoveCircle", "Filled", Icons.Filled.RemoveCircle),
        IconData("RemoveCircleOutline", "Filled", Icons.Filled.RemoveCircleOutline),
        IconData("RemoveDone", "Filled", Icons.Filled.RemoveDone),
        IconData("RemoveFromQueue", "Filled", Icons.Filled.RemoveFromQueue),
        IconData("RemoveModerator", "Filled", Icons.Filled.RemoveModerator),
        IconData("RemoveRedEye", "Filled", Icons.Filled.RemoveRedEye),
        IconData("RemoveRoad", "Filled", Icons.Filled.RemoveRoad),
        IconData("RemoveShoppingCart", "Filled", Icons.Filled.RemoveShoppingCart),
        IconData("Reorder", "Filled", Icons.Filled.Reorder),
        IconData("Repartition", "Filled", Icons.Filled.Repartition),
        IconData("Repeat", "Filled", Icons.Filled.Repeat),
        IconData("RepeatOn", "Filled", Icons.Filled.RepeatOn),
        IconData("RepeatOne", "Filled", Icons.Filled.RepeatOne),
        IconData("RepeatOneOn", "Filled", Icons.Filled.RepeatOneOn),
        IconData("Replay", "Filled", Icons.Filled.Replay),
        IconData("Replay10", "Filled", Icons.Filled.Replay10),
        IconData("Replay30", "Filled", Icons.Filled.Replay30),
        IconData("Replay5", "Filled", Icons.Filled.Replay5),
        IconData("ReplayCircleFilled", "Filled", Icons.Filled.ReplayCircleFilled),
        IconData("Report", "Filled", Icons.Filled.Report),
        IconData("ReportGmailerrorred", "Filled", Icons.Filled.ReportGmailerrorred),
        IconData("ReportOff", "Filled", Icons.Filled.ReportOff),
        IconData("ReportProblem", "Filled", Icons.Filled.ReportProblem),
        IconData("RequestPage", "Filled", Icons.Filled.RequestPage),
        IconData("RequestQuote", "Filled", Icons.Filled.RequestQuote),
        IconData("ResetTv", "Filled", Icons.Filled.ResetTv),
        IconData("RestartAlt", "Filled", Icons.Filled.RestartAlt),
        IconData("Restaurant", "Filled", Icons.Filled.Restaurant),
        IconData("RestaurantMenu", "Filled", Icons.Filled.RestaurantMenu),
        IconData("Restore", "Filled", Icons.Filled.Restore),
        IconData("RestoreFromTrash", "Filled", Icons.Filled.RestoreFromTrash),
        IconData("RestorePage", "Filled", Icons.Filled.RestorePage),
        IconData("Reviews", "Filled", Icons.Filled.Reviews),
        IconData("RiceBowl", "Filled", Icons.Filled.RiceBowl),
        IconData("RingVolume", "Filled", Icons.Filled.RingVolume),
        IconData("Rocket", "Filled", Icons.Filled.Rocket),
        IconData("RocketLaunch", "Filled", Icons.Filled.RocketLaunch),
        IconData("RollerShades", "Filled", Icons.Filled.RollerShades),
        IconData("RollerShadesClosed", "Filled", Icons.Filled.RollerShadesClosed),
        IconData("RollerSkating", "Filled", Icons.Filled.RollerSkating),
        IconData("Roofing", "Filled", Icons.Filled.Roofing),
        IconData("Room", "Filled", Icons.Filled.Room),
        IconData("RoomPreferences", "Filled", Icons.Filled.RoomPreferences),
        IconData("RoomService", "Filled", Icons.Filled.RoomService),
        IconData("Rotate90DegreesCcw", "Filled", Icons.Filled.Rotate90DegreesCcw),
        IconData("Rotate90DegreesCw", "Filled", Icons.Filled.Rotate90DegreesCw),
        IconData("RoundaboutLeft", "Filled", Icons.Filled.RoundaboutLeft),
        IconData("RoundaboutRight", "Filled", Icons.Filled.RoundaboutRight),
        IconData("RoundedCorner", "Filled", Icons.Filled.RoundedCorner),
        IconData("Route", "Filled", Icons.Filled.Route),
        IconData("Router", "Filled", Icons.Filled.Router),
        IconData("Rowing", "Filled", Icons.Filled.Rowing),
        IconData("RssFeed", "Filled", Icons.Filled.RssFeed),
        IconData("Rsvp", "Filled", Icons.Filled.Rsvp),
        IconData("RuleFolder", "Filled", Icons.Filled.RuleFolder),
        IconData("RunCircle", "Filled", Icons.Filled.RunCircle),
        IconData("RunningWithErrors", "Filled", Icons.Filled.RunningWithErrors),
        IconData("RvHookup", "Filled", Icons.Filled.RvHookup),
        IconData("SafetyCheck", "Filled", Icons.Filled.SafetyCheck),
        IconData("SafetyDivider", "Filled", Icons.Filled.SafetyDivider),
        IconData("Sailing", "Filled", Icons.Filled.Sailing),
        IconData("Sanitizer", "Filled", Icons.Filled.Sanitizer),
        IconData("Satellite", "Filled", Icons.Filled.Satellite),
        IconData("SatelliteAlt", "Filled", Icons.Filled.SatelliteAlt),
        IconData("Save", "Filled", Icons.Filled.Save),
        IconData("SaveAlt", "Filled", Icons.Filled.SaveAlt),
        IconData("SaveAs", "Filled", Icons.Filled.SaveAs),
        IconData("SavedSearch", "Filled", Icons.Filled.SavedSearch),
        IconData("Savings", "Filled", Icons.Filled.Savings),
        IconData("Scale", "Filled", Icons.Filled.Scale),
        IconData("Scanner", "Filled", Icons.Filled.Scanner),
        IconData("ScatterPlot", "Filled", Icons.Filled.ScatterPlot),
        IconData("Schedule", "Filled", Icons.Filled.Schedule),
        IconData("Schema", "Filled", Icons.Filled.Schema),
        IconData("School", "Filled", Icons.Filled.School),
        IconData("Science", "Filled", Icons.Filled.Science),
        IconData("Score", "Filled", Icons.Filled.Score),
        IconData("Scoreboard", "Filled", Icons.Filled.Scoreboard),
        IconData("ScreenLockLandscape", "Filled", Icons.Filled.ScreenLockLandscape),
        IconData("ScreenLockPortrait", "Filled", Icons.Filled.ScreenLockPortrait),
        IconData("ScreenLockRotation", "Filled", Icons.Filled.ScreenLockRotation),
        IconData("ScreenRotation", "Filled", Icons.Filled.ScreenRotation),
        IconData("ScreenRotationAlt", "Filled", Icons.Filled.ScreenRotationAlt),
        IconData("ScreenSearchDesktop", "Filled", Icons.Filled.ScreenSearchDesktop),
        IconData("Screenshot", "Filled", Icons.Filled.Screenshot),
        IconData("ScreenshotMonitor", "Filled", Icons.Filled.ScreenshotMonitor),
        IconData("ScubaDiving", "Filled", Icons.Filled.ScubaDiving),
        IconData("Sd", "Filled", Icons.Filled.Sd),
        IconData("SdCard", "Filled", Icons.Filled.SdCard),
        IconData("SdCardAlert", "Filled", Icons.Filled.SdCardAlert),
        IconData("SdStorage", "Filled", Icons.Filled.SdStorage),
        IconData("SearchOff", "Filled", Icons.Filled.SearchOff),
        IconData("Security", "Filled", Icons.Filled.Security),
        IconData("SecurityUpdate", "Filled", Icons.Filled.SecurityUpdate),
        IconData("SecurityUpdateGood", "Filled", Icons.Filled.SecurityUpdateGood),
        IconData("SecurityUpdateWarning", "Filled", Icons.Filled.SecurityUpdateWarning),
        IconData("SelectAll", "Filled", Icons.Filled.SelectAll),
        IconData("SelfImprovement", "Filled", Icons.Filled.SelfImprovement),
        IconData("Sell", "Filled", Icons.Filled.Sell),
        IconData("SendTimeExtension", "Filled", Icons.Filled.SendTimeExtension),
        IconData("SensorDoor", "Filled", Icons.Filled.SensorDoor),
        IconData("SensorOccupied", "Filled", Icons.Filled.SensorOccupied),
        IconData("SensorWindow", "Filled", Icons.Filled.SensorWindow),
        IconData("Sensors", "Filled", Icons.Filled.Sensors),
        IconData("SensorsOff", "Filled", Icons.Filled.SensorsOff),
        IconData("SentimentDissatisfied", "Filled", Icons.Filled.SentimentDissatisfied),
        IconData("SentimentNeutral", "Filled", Icons.Filled.SentimentNeutral),
        IconData("SentimentSatisfied", "Filled", Icons.Filled.SentimentSatisfied),
        IconData("SentimentSatisfiedAlt", "Filled", Icons.Filled.SentimentSatisfiedAlt),
        IconData("SentimentVeryDissatisfied", "Filled", Icons.Filled.SentimentVeryDissatisfied),
        IconData("SentimentVerySatisfied", "Filled", Icons.Filled.SentimentVerySatisfied),
        IconData("SetMeal", "Filled", Icons.Filled.SetMeal),
        IconData("SettingsAccessibility", "Filled", Icons.Filled.SettingsAccessibility),
        IconData("SettingsApplications", "Filled", Icons.Filled.SettingsApplications),
        IconData("SettingsBackupRestore", "Filled", Icons.Filled.SettingsBackupRestore),
        IconData("SettingsBluetooth", "Filled", Icons.Filled.SettingsBluetooth),
        IconData("SettingsBrightness", "Filled", Icons.Filled.SettingsBrightness),
        IconData("SettingsCell", "Filled", Icons.Filled.SettingsCell),
        IconData("SettingsEthernet", "Filled", Icons.Filled.SettingsEthernet),
        IconData("SettingsInputAntenna", "Filled", Icons.Filled.SettingsInputAntenna),
        IconData("SettingsInputComponent", "Filled", Icons.Filled.SettingsInputComponent),
        IconData("SettingsInputComposite", "Filled", Icons.Filled.SettingsInputComposite),
        IconData("SettingsInputHdmi", "Filled", Icons.Filled.SettingsInputHdmi),
        IconData("SettingsInputSvideo", "Filled", Icons.Filled.SettingsInputSvideo),
        IconData("SettingsOverscan", "Filled", Icons.Filled.SettingsOverscan),
        IconData("SettingsPhone", "Filled", Icons.Filled.SettingsPhone),
        IconData("SettingsPower", "Filled", Icons.Filled.SettingsPower),
        IconData("SettingsRemote", "Filled", Icons.Filled.SettingsRemote),
        IconData("SettingsSuggest", "Filled", Icons.Filled.SettingsSuggest),
        IconData("SettingsSystemDaydream", "Filled", Icons.Filled.SettingsSystemDaydream),
        IconData("SettingsVoice", "Filled", Icons.Filled.SettingsVoice),
        IconData("SevereCold", "Filled", Icons.Filled.SevereCold),
        IconData("ShapeLine", "Filled", Icons.Filled.ShapeLine),
        IconData("ShareLocation", "Filled", Icons.Filled.ShareLocation),
        IconData("Shield", "Filled", Icons.Filled.Shield),
        IconData("ShieldMoon", "Filled", Icons.Filled.ShieldMoon),
        IconData("Shop", "Filled", Icons.Filled.Shop),
        IconData("Shop2", "Filled", Icons.Filled.Shop2),
        IconData("ShopTwo", "Filled", Icons.Filled.ShopTwo),
        IconData("ShoppingBag", "Filled", Icons.Filled.ShoppingBag),
        IconData("ShoppingBasket", "Filled", Icons.Filled.ShoppingBasket),
        IconData("ShoppingCartCheckout", "Filled", Icons.Filled.ShoppingCartCheckout),
        IconData("Shower", "Filled", Icons.Filled.Shower),
        IconData("Shuffle", "Filled", Icons.Filled.Shuffle),
        IconData("ShuffleOn", "Filled", Icons.Filled.ShuffleOn),
        IconData("ShutterSpeed", "Filled", Icons.Filled.ShutterSpeed),
        IconData("Sick", "Filled", Icons.Filled.Sick),
        IconData("SignLanguage", "Filled", Icons.Filled.SignLanguage),
        IconData("SignalCellular0Bar", "Filled", Icons.Filled.SignalCellular0Bar),
        IconData("SignalCellular4Bar", "Filled", Icons.Filled.SignalCellular4Bar),
        IconData("SignalCellularAlt", "Filled", Icons.Filled.SignalCellularAlt),
        IconData("SignalCellularAlt1Bar", "Filled", Icons.Filled.SignalCellularAlt1Bar),
        IconData("SignalCellularAlt2Bar", "Filled", Icons.Filled.SignalCellularAlt2Bar),
        IconData("SignalCellularConnectedNoInternet0Bar", "Filled", Icons.Filled.SignalCellularConnectedNoInternet0Bar),
        IconData("SignalCellularConnectedNoInternet4Bar", "Filled", Icons.Filled.SignalCellularConnectedNoInternet4Bar),
        IconData("SignalCellularNoSim", "Filled", Icons.Filled.SignalCellularNoSim),
        IconData("SignalCellularNodata", "Filled", Icons.Filled.SignalCellularNodata),
        IconData("SignalCellularNull", "Filled", Icons.Filled.SignalCellularNull),
        IconData("SignalCellularOff", "Filled", Icons.Filled.SignalCellularOff),
        IconData("SignalWifi0Bar", "Filled", Icons.Filled.SignalWifi0Bar),
        IconData("SignalWifi4Bar", "Filled", Icons.Filled.SignalWifi4Bar),
        IconData("SignalWifi4BarLock", "Filled", Icons.Filled.SignalWifi4BarLock),
        IconData("SignalWifiBad", "Filled", Icons.Filled.SignalWifiBad),
        IconData("SignalWifiConnectedNoInternet4", "Filled", Icons.Filled.SignalWifiConnectedNoInternet4),
        IconData("SignalWifiOff", "Filled", Icons.Filled.SignalWifiOff),
        IconData("SignalWifiStatusbar4Bar", "Filled", Icons.Filled.SignalWifiStatusbar4Bar),
        IconData("SignalWifiStatusbarConnectedNoInternet4", "Filled", Icons.Filled.SignalWifiStatusbarConnectedNoInternet4),
        IconData("SignalWifiStatusbarNull", "Filled", Icons.Filled.SignalWifiStatusbarNull),
        IconData("Signpost", "Filled", Icons.Filled.Signpost),
        IconData("SimCard", "Filled", Icons.Filled.SimCard),
        IconData("SimCardAlert", "Filled", Icons.Filled.SimCardAlert),
        IconData("SimCardDownload", "Filled", Icons.Filled.SimCardDownload),
        IconData("SingleBed", "Filled", Icons.Filled.SingleBed),
        IconData("Sip", "Filled", Icons.Filled.Sip),
        IconData("Skateboarding", "Filled", Icons.Filled.Skateboarding),
        IconData("SkipNext", "Filled", Icons.Filled.SkipNext),
        IconData("SkipPrevious", "Filled", Icons.Filled.SkipPrevious),
        IconData("Sledding", "Filled", Icons.Filled.Sledding),
        IconData("Slideshow", "Filled", Icons.Filled.Slideshow),
        IconData("SlowMotionVideo", "Filled", Icons.Filled.SlowMotionVideo),
        IconData("SmartButton", "Filled", Icons.Filled.SmartButton),
        IconData("SmartDisplay", "Filled", Icons.Filled.SmartDisplay),
        IconData("SmartScreen", "Filled", Icons.Filled.SmartScreen),
        IconData("SmartToy", "Filled", Icons.Filled.SmartToy),
        IconData("Smartphone", "Filled", Icons.Filled.Smartphone),
        IconData("SmokeFree", "Filled", Icons.Filled.SmokeFree),
        IconData("SmokingRooms", "Filled", Icons.Filled.SmokingRooms),
        IconData("Sms", "Filled", Icons.Filled.Sms),
        IconData("SmsFailed", "Filled", Icons.Filled.SmsFailed),
        IconData("SnippetFolder", "Filled", Icons.Filled.SnippetFolder),
        IconData("Snooze", "Filled", Icons.Filled.Snooze),
        IconData("Snowboarding", "Filled", Icons.Filled.Snowboarding),
        IconData("Snowmobile", "Filled", Icons.Filled.Snowmobile),
        IconData("Snowshoeing", "Filled", Icons.Filled.Snowshoeing),
        IconData("Soap", "Filled", Icons.Filled.Soap),
        IconData("SocialDistance", "Filled", Icons.Filled.SocialDistance),
        IconData("SolarPower", "Filled", Icons.Filled.SolarPower),
        IconData("SortByAlpha", "Filled", Icons.Filled.SortByAlpha),
        IconData("Sos", "Filled", Icons.Filled.Sos),
        IconData("SoupKitchen", "Filled", Icons.Filled.SoupKitchen),
        IconData("Source", "Filled", Icons.Filled.Source),
        IconData("South", "Filled", Icons.Filled.South),
        IconData("SouthAmerica", "Filled", Icons.Filled.SouthAmerica),
        IconData("SouthEast", "Filled", Icons.Filled.SouthEast),
        IconData("SouthWest", "Filled", Icons.Filled.SouthWest),
        IconData("Spa", "Filled", Icons.Filled.Spa),
        IconData("SpaceBar", "Filled", Icons.Filled.SpaceBar),
        IconData("SpaceDashboard", "Filled", Icons.Filled.SpaceDashboard),
        IconData("SpatialAudio", "Filled", Icons.Filled.SpatialAudio),
        IconData("SpatialAudioOff", "Filled", Icons.Filled.SpatialAudioOff),
        IconData("SpatialTracking", "Filled", Icons.Filled.SpatialTracking),
        IconData("Speaker", "Filled", Icons.Filled.Speaker),
        IconData("SpeakerGroup", "Filled", Icons.Filled.SpeakerGroup),
        IconData("SpeakerNotesOff", "Filled", Icons.Filled.SpeakerNotesOff),
        IconData("SpeakerPhone", "Filled", Icons.Filled.SpeakerPhone),
        IconData("Speed", "Filled", Icons.Filled.Speed),
        IconData("Spellcheck", "Filled", Icons.Filled.Spellcheck),
        IconData("Splitscreen", "Filled", Icons.Filled.Splitscreen),
        IconData("Spoke", "Filled", Icons.Filled.Spoke),
        IconData("Sports", "Filled", Icons.Filled.Sports),
        IconData("SportsBar", "Filled", Icons.Filled.SportsBar),
        IconData("SportsBaseball", "Filled", Icons.Filled.SportsBaseball),
        IconData("SportsBasketball", "Filled", Icons.Filled.SportsBasketball),
        IconData("SportsCricket", "Filled", Icons.Filled.SportsCricket),
        IconData("SportsEsports", "Filled", Icons.Filled.SportsEsports),
        IconData("SportsFootball", "Filled", Icons.Filled.SportsFootball),
        IconData("SportsGolf", "Filled", Icons.Filled.SportsGolf),
        IconData("SportsGymnastics", "Filled", Icons.Filled.SportsGymnastics),
        IconData("SportsHandball", "Filled", Icons.Filled.SportsHandball),
        IconData("SportsHockey", "Filled", Icons.Filled.SportsHockey),
        IconData("SportsKabaddi", "Filled", Icons.Filled.SportsKabaddi),
        IconData("SportsMartialArts", "Filled", Icons.Filled.SportsMartialArts),
        IconData("SportsMma", "Filled", Icons.Filled.SportsMma),
        IconData("SportsMotorsports", "Filled", Icons.Filled.SportsMotorsports),
        IconData("SportsRugby", "Filled", Icons.Filled.SportsRugby),
        IconData("SportsScore", "Filled", Icons.Filled.SportsScore),
        IconData("SportsSoccer", "Filled", Icons.Filled.SportsSoccer),
        IconData("SportsTennis", "Filled", Icons.Filled.SportsTennis),
        IconData("SportsVolleyball", "Filled", Icons.Filled.SportsVolleyball),
        IconData("Square", "Filled", Icons.Filled.Square),
        IconData("SquareFoot", "Filled", Icons.Filled.SquareFoot),
        IconData("SsidChart", "Filled", Icons.Filled.SsidChart),
        IconData("StackedBarChart", "Filled", Icons.Filled.StackedBarChart),
        IconData("StackedLineChart", "Filled", Icons.Filled.StackedLineChart),
        IconData("Stadium", "Filled", Icons.Filled.Stadium),
        IconData("Stairs", "Filled", Icons.Filled.Stairs),
        IconData("StarBorder", "Filled", Icons.Filled.StarBorder),
        IconData("StarBorderPurple500", "Filled", Icons.Filled.StarBorderPurple500),
        IconData("StarOutline", "Filled", Icons.Filled.StarOutline),
        IconData("StarPurple500", "Filled", Icons.Filled.StarPurple500),
        IconData("StarRate", "Filled", Icons.Filled.StarRate),
        IconData("Stars", "Filled", Icons.Filled.Stars),
        IconData("Start", "Filled", Icons.Filled.Start),
        IconData("StayCurrentLandscape", "Filled", Icons.Filled.StayCurrentLandscape),
        IconData("StayCurrentPortrait", "Filled", Icons.Filled.StayCurrentPortrait),
        IconData("StayPrimaryLandscape", "Filled", Icons.Filled.StayPrimaryLandscape),
        IconData("StayPrimaryPortrait", "Filled", Icons.Filled.StayPrimaryPortrait),
        IconData("Stop", "Filled", Icons.Filled.Stop),
        IconData("StopCircle", "Filled", Icons.Filled.StopCircle),
        IconData("Storage", "Filled", Icons.Filled.Storage),
        IconData("Store", "Filled", Icons.Filled.Store),
        IconData("StoreMallDirectory", "Filled", Icons.Filled.StoreMallDirectory),
        IconData("Storefront", "Filled", Icons.Filled.Storefront),
        IconData("Storm", "Filled", Icons.Filled.Storm),
        IconData("Straight", "Filled", Icons.Filled.Straight),
        IconData("Straighten", "Filled", Icons.Filled.Straighten),
        IconData("Stream", "Filled", Icons.Filled.Stream),
        IconData("Streetview", "Filled", Icons.Filled.Streetview),
        IconData("StrikethroughS", "Filled", Icons.Filled.StrikethroughS),
        IconData("Stroller", "Filled", Icons.Filled.Stroller),
        IconData("Style", "Filled", Icons.Filled.Style),
        IconData("SubdirectoryArrowLeft", "Filled", Icons.Filled.SubdirectoryArrowLeft),
        IconData("SubdirectoryArrowRight", "Filled", Icons.Filled.SubdirectoryArrowRight),
        IconData("Subscript", "Filled", Icons.Filled.Subscript),
        IconData("Subscriptions", "Filled", Icons.Filled.Subscriptions),
        IconData("Subtitles", "Filled", Icons.Filled.Subtitles),
        IconData("SubtitlesOff", "Filled", Icons.Filled.SubtitlesOff),
        IconData("Subway", "Filled", Icons.Filled.Subway),
        IconData("Summarize", "Filled", Icons.Filled.Summarize),
        IconData("Superscript", "Filled", Icons.Filled.Superscript),
        IconData("SupervisedUserCircle", "Filled", Icons.Filled.SupervisedUserCircle),
        IconData("SupervisorAccount", "Filled", Icons.Filled.SupervisorAccount),
        IconData("Support", "Filled", Icons.Filled.Support),
        IconData("SupportAgent", "Filled", Icons.Filled.SupportAgent),
        IconData("Surfing", "Filled", Icons.Filled.Surfing),
        IconData("SurroundSound", "Filled", Icons.Filled.SurroundSound),
        IconData("SwapCalls", "Filled", Icons.Filled.SwapCalls),
        IconData("SwapHoriz", "Filled", Icons.Filled.SwapHoriz),
        IconData("SwapHorizontalCircle", "Filled", Icons.Filled.SwapHorizontalCircle),
        IconData("SwapVert", "Filled", Icons.Filled.SwapVert),
        IconData("SwapVerticalCircle", "Filled", Icons.Filled.SwapVerticalCircle),
        IconData("Swipe", "Filled", Icons.Filled.Swipe),
        IconData("SwipeDown", "Filled", Icons.Filled.SwipeDown),
        IconData("SwipeDownAlt", "Filled", Icons.Filled.SwipeDownAlt),
        IconData("SwipeLeft", "Filled", Icons.Filled.SwipeLeft),
        IconData("SwipeLeftAlt", "Filled", Icons.Filled.SwipeLeftAlt),
        IconData("SwipeRight", "Filled", Icons.Filled.SwipeRight),
        IconData("SwipeRightAlt", "Filled", Icons.Filled.SwipeRightAlt),
        IconData("SwipeUp", "Filled", Icons.Filled.SwipeUp),
        IconData("SwipeUpAlt", "Filled", Icons.Filled.SwipeUpAlt),
        IconData("SwipeVertical", "Filled", Icons.Filled.SwipeVertical),
        IconData("SwitchAccessShortcut", "Filled", Icons.Filled.SwitchAccessShortcut),
        IconData("SwitchAccessShortcutAdd", "Filled", Icons.Filled.SwitchAccessShortcutAdd),
        IconData("SwitchAccount", "Filled", Icons.Filled.SwitchAccount),
        IconData("SwitchCamera", "Filled", Icons.Filled.SwitchCamera),
        IconData("SwitchLeft", "Filled", Icons.Filled.SwitchLeft),
        IconData("SwitchRight", "Filled", Icons.Filled.SwitchRight),
        IconData("SwitchVideo", "Filled", Icons.Filled.SwitchVideo),
        IconData("Synagogue", "Filled", Icons.Filled.Synagogue),
        IconData("Sync", "Filled", Icons.Filled.Sync),
        IconData("SyncAlt", "Filled", Icons.Filled.SyncAlt),
        IconData("SyncDisabled", "Filled", Icons.Filled.SyncDisabled),
        IconData("SyncLock", "Filled", Icons.Filled.SyncLock),
        IconData("SyncProblem", "Filled", Icons.Filled.SyncProblem),
        IconData("SystemSecurityUpdate", "Filled", Icons.Filled.SystemSecurityUpdate),
        IconData("SystemSecurityUpdateGood", "Filled", Icons.Filled.SystemSecurityUpdateGood),
        IconData("SystemSecurityUpdateWarning", "Filled", Icons.Filled.SystemSecurityUpdateWarning),
        IconData("SystemUpdate", "Filled", Icons.Filled.SystemUpdate),
        IconData("SystemUpdateAlt", "Filled", Icons.Filled.SystemUpdateAlt),
        IconData("Tab", "Filled", Icons.Filled.Tab),
        IconData("TabUnselected", "Filled", Icons.Filled.TabUnselected),
        IconData("TableBar", "Filled", Icons.Filled.TableBar),
        IconData("TableChart", "Filled", Icons.Filled.TableChart),
        IconData("TableRestaurant", "Filled", Icons.Filled.TableRestaurant),
        IconData("TableRows", "Filled", Icons.Filled.TableRows),
        IconData("TableView", "Filled", Icons.Filled.TableView),
        IconData("Tablet", "Filled", Icons.Filled.Tablet),
        IconData("TabletAndroid", "Filled", Icons.Filled.TabletAndroid),
        IconData("TabletMac", "Filled", Icons.Filled.TabletMac),
        IconData("Tag", "Filled", Icons.Filled.Tag),
        IconData("TagFaces", "Filled", Icons.Filled.TagFaces),
        IconData("TakeoutDining", "Filled", Icons.Filled.TakeoutDining),
        IconData("TapAndPlay", "Filled", Icons.Filled.TapAndPlay),
        IconData("Tapas", "Filled", Icons.Filled.Tapas),
        IconData("Task", "Filled", Icons.Filled.Task),
        IconData("TaskAlt", "Filled", Icons.Filled.TaskAlt),
        IconData("TaxiAlert", "Filled", Icons.Filled.TaxiAlert),
        IconData("TempleBuddhist", "Filled", Icons.Filled.TempleBuddhist),
        IconData("TempleHindu", "Filled", Icons.Filled.TempleHindu),
        IconData("Terminal", "Filled", Icons.Filled.Terminal),
        IconData("Terrain", "Filled", Icons.Filled.Terrain),
        IconData("TextDecrease", "Filled", Icons.Filled.TextDecrease),
        IconData("TextFields", "Filled", Icons.Filled.TextFields),
        IconData("TextFormat", "Filled", Icons.Filled.TextFormat),
        IconData("TextIncrease", "Filled", Icons.Filled.TextIncrease),
        IconData("TextRotateUp", "Filled", Icons.Filled.TextRotateUp),
        IconData("TextRotateVertical", "Filled", Icons.Filled.TextRotateVertical),
        IconData("TextRotationAngledown", "Filled", Icons.Filled.TextRotationAngledown),
        IconData("TextRotationAngleup", "Filled", Icons.Filled.TextRotationAngleup),
        IconData("TextRotationDown", "Filled", Icons.Filled.TextRotationDown),
        IconData("TextRotationNone", "Filled", Icons.Filled.TextRotationNone),
        IconData("Textsms", "Filled", Icons.Filled.Textsms),
        IconData("Texture", "Filled", Icons.Filled.Texture),
        IconData("TheaterComedy", "Filled", Icons.Filled.TheaterComedy),
        IconData("Theaters", "Filled", Icons.Filled.Theaters),
        IconData("Thermostat", "Filled", Icons.Filled.Thermostat),
        IconData("ThermostatAuto", "Filled", Icons.Filled.ThermostatAuto),
        IconData("ThumbDown", "Filled", Icons.Filled.ThumbDown),
        IconData("ThumbDownAlt", "Filled", Icons.Filled.ThumbDownAlt),
        IconData("ThumbDownOffAlt", "Filled", Icons.Filled.ThumbDownOffAlt),
        IconData("ThumbUpAlt", "Filled", Icons.Filled.ThumbUpAlt),
        IconData("ThumbUpOffAlt", "Filled", Icons.Filled.ThumbUpOffAlt),
        IconData("ThumbsUpDown", "Filled", Icons.Filled.ThumbsUpDown),
        IconData("Thunderstorm", "Filled", Icons.Filled.Thunderstorm),
        IconData("TimeToLeave", "Filled", Icons.Filled.TimeToLeave),
        IconData("Timelapse", "Filled", Icons.Filled.Timelapse),
        IconData("Timeline", "Filled", Icons.Filled.Timeline),
        IconData("Timer", "Filled", Icons.Filled.Timer),
        IconData("Timer10", "Filled", Icons.Filled.Timer10),
        IconData("Timer10Select", "Filled", Icons.Filled.Timer10Select),
        IconData("Timer3", "Filled", Icons.Filled.Timer3),
        IconData("Timer3Select", "Filled", Icons.Filled.Timer3Select),
        IconData("TimerOff", "Filled", Icons.Filled.TimerOff),
        IconData("TipsAndUpdates", "Filled", Icons.Filled.TipsAndUpdates),
        IconData("TireRepair", "Filled", Icons.Filled.TireRepair),
        IconData("Title", "Filled", Icons.Filled.Title),
        IconData("Today", "Filled", Icons.Filled.Today),
        IconData("ToggleOff", "Filled", Icons.Filled.ToggleOff),
        IconData("ToggleOn", "Filled", Icons.Filled.ToggleOn),
        IconData("Token", "Filled", Icons.Filled.Token),
        IconData("Toll", "Filled", Icons.Filled.Toll),
        IconData("Tonality", "Filled", Icons.Filled.Tonality),
        IconData("Topic", "Filled", Icons.Filled.Topic),
        IconData("Tornado", "Filled", Icons.Filled.Tornado),
        IconData("TouchApp", "Filled", Icons.Filled.TouchApp),
        IconData("Tour", "Filled", Icons.Filled.Tour),
        IconData("Toys", "Filled", Icons.Filled.Toys),
        IconData("TrackChanges", "Filled", Icons.Filled.TrackChanges),
        IconData("Traffic", "Filled", Icons.Filled.Traffic),
        IconData("Train", "Filled", Icons.Filled.Train),
        IconData("Tram", "Filled", Icons.Filled.Tram),
        IconData("Transcribe", "Filled", Icons.Filled.Transcribe),
        IconData("TransferWithinAStation", "Filled", Icons.Filled.TransferWithinAStation),
        IconData("Transform", "Filled", Icons.Filled.Transform),
        IconData("Transgender", "Filled", Icons.Filled.Transgender),
        IconData("TransitEnterexit", "Filled", Icons.Filled.TransitEnterexit),
        IconData("Translate", "Filled", Icons.Filled.Translate),
        IconData("TravelExplore", "Filled", Icons.Filled.TravelExplore),
        IconData("TripOrigin", "Filled", Icons.Filled.TripOrigin),
        IconData("Troubleshoot", "Filled", Icons.Filled.Troubleshoot),
        IconData("Try", "Filled", Icons.Filled.Try),
        IconData("Tsunami", "Filled", Icons.Filled.Tsunami),
        IconData("Tty", "Filled", Icons.Filled.Tty),
        IconData("Tune", "Filled", Icons.Filled.Tune),
        IconData("Tungsten", "Filled", Icons.Filled.Tungsten),
        IconData("TurnLeft", "Filled", Icons.Filled.TurnLeft),
        IconData("TurnRight", "Filled", Icons.Filled.TurnRight),
        IconData("TurnSharpLeft", "Filled", Icons.Filled.TurnSharpLeft),
        IconData("TurnSharpRight", "Filled", Icons.Filled.TurnSharpRight),
        IconData("TurnSlightLeft", "Filled", Icons.Filled.TurnSlightLeft),
        IconData("TurnSlightRight", "Filled", Icons.Filled.TurnSlightRight),
        IconData("TurnedIn", "Filled", Icons.Filled.TurnedIn),
        IconData("TurnedInNot", "Filled", Icons.Filled.TurnedInNot),
        IconData("Tv", "Filled", Icons.Filled.Tv),
        IconData("TvOff", "Filled", Icons.Filled.TvOff),
        IconData("TwoWheeler", "Filled", Icons.Filled.TwoWheeler),
        IconData("TypeSpecimen", "Filled", Icons.Filled.TypeSpecimen),
        IconData("UTurnLeft", "Filled", Icons.Filled.UTurnLeft),
        IconData("UTurnRight", "Filled", Icons.Filled.UTurnRight),
        IconData("Umbrella", "Filled", Icons.Filled.Umbrella),
        IconData("Unarchive", "Filled", Icons.Filled.Unarchive),
        IconData("UnfoldLess", "Filled", Icons.Filled.UnfoldLess),
        IconData("UnfoldLessDouble", "Filled", Icons.Filled.UnfoldLessDouble),
        IconData("UnfoldMore", "Filled", Icons.Filled.UnfoldMore),
        IconData("UnfoldMoreDouble", "Filled", Icons.Filled.UnfoldMoreDouble),
        IconData("Unpublished", "Filled", Icons.Filled.Unpublished),
        IconData("Unsubscribe", "Filled", Icons.Filled.Unsubscribe),
        IconData("Upcoming", "Filled", Icons.Filled.Upcoming),
        IconData("Update", "Filled", Icons.Filled.Update),
        IconData("UpdateDisabled", "Filled", Icons.Filled.UpdateDisabled),
        IconData("Upgrade", "Filled", Icons.Filled.Upgrade),
        IconData("Upload", "Filled", Icons.Filled.Upload),
        IconData("UploadFile", "Filled", Icons.Filled.UploadFile),
        IconData("Usb", "Filled", Icons.Filled.Usb),
        IconData("UsbOff", "Filled", Icons.Filled.UsbOff),
        IconData("Vaccines", "Filled", Icons.Filled.Vaccines),
        IconData("VapeFree", "Filled", Icons.Filled.VapeFree),
        IconData("VapingRooms", "Filled", Icons.Filled.VapingRooms),
        IconData("Verified", "Filled", Icons.Filled.Verified),
        IconData("VerifiedUser", "Filled", Icons.Filled.VerifiedUser),
        IconData("VerticalAlignBottom", "Filled", Icons.Filled.VerticalAlignBottom),
        IconData("VerticalAlignCenter", "Filled", Icons.Filled.VerticalAlignCenter),
        IconData("VerticalAlignTop", "Filled", Icons.Filled.VerticalAlignTop),
        IconData("VerticalDistribute", "Filled", Icons.Filled.VerticalDistribute),
        IconData("VerticalShades", "Filled", Icons.Filled.VerticalShades),
        IconData("VerticalShadesClosed", "Filled", Icons.Filled.VerticalShadesClosed),
        IconData("VerticalSplit", "Filled", Icons.Filled.VerticalSplit),
        IconData("Vibration", "Filled", Icons.Filled.Vibration),
        IconData("VideoCall", "Filled", Icons.Filled.VideoCall),
        IconData("VideoCameraBack", "Filled", Icons.Filled.VideoCameraBack),
        IconData("VideoCameraFront", "Filled", Icons.Filled.VideoCameraFront),
        IconData("VideoChat", "Filled", Icons.Filled.VideoChat),
        IconData("VideoFile", "Filled", Icons.Filled.VideoFile),
        IconData("VideoLabel", "Filled", Icons.Filled.VideoLabel),
        IconData("VideoLibrary", "Filled", Icons.Filled.VideoLibrary),
        IconData("VideoSettings", "Filled", Icons.Filled.VideoSettings),
        IconData("VideoStable", "Filled", Icons.Filled.VideoStable),
        IconData("Videocam", "Filled", Icons.Filled.Videocam),
        IconData("VideocamOff", "Filled", Icons.Filled.VideocamOff),
        IconData("VideogameAsset", "Filled", Icons.Filled.VideogameAsset),
        IconData("VideogameAssetOff", "Filled", Icons.Filled.VideogameAssetOff),
        IconData("ViewAgenda", "Filled", Icons.Filled.ViewAgenda),
        IconData("ViewArray", "Filled", Icons.Filled.ViewArray),
        IconData("ViewCarousel", "Filled", Icons.Filled.ViewCarousel),
        IconData("ViewColumn", "Filled", Icons.Filled.ViewColumn),
        IconData("ViewComfy", "Filled", Icons.Filled.ViewComfy),
        IconData("ViewComfyAlt", "Filled", Icons.Filled.ViewComfyAlt),
        IconData("ViewCompact", "Filled", Icons.Filled.ViewCompact),
        IconData("ViewCompactAlt", "Filled", Icons.Filled.ViewCompactAlt),
        IconData("ViewCozy", "Filled", Icons.Filled.ViewCozy),
        IconData("ViewDay", "Filled", Icons.Filled.ViewDay),
        IconData("ViewHeadline", "Filled", Icons.Filled.ViewHeadline),
        IconData("ViewInAr", "Filled", Icons.Filled.ViewInAr),
        IconData("ViewKanban", "Filled", Icons.Filled.ViewKanban),
        IconData("ViewModule", "Filled", Icons.Filled.ViewModule),
        IconData("ViewStream", "Filled", Icons.Filled.ViewStream),
        IconData("ViewTimeline", "Filled", Icons.Filled.ViewTimeline),
        IconData("ViewWeek", "Filled", Icons.Filled.ViewWeek),
        IconData("Vignette", "Filled", Icons.Filled.Vignette),
        IconData("Villa", "Filled", Icons.Filled.Villa),
        IconData("Visibility", "Filled", Icons.Filled.Visibility),
        IconData("VisibilityOff", "Filled", Icons.Filled.VisibilityOff),
        IconData("VoiceChat", "Filled", Icons.Filled.VoiceChat),
        IconData("VoiceOverOff", "Filled", Icons.Filled.VoiceOverOff),
        IconData("Voicemail", "Filled", Icons.Filled.Voicemail),
        IconData("Volcano", "Filled", Icons.Filled.Volcano),
        IconData("VolunteerActivism", "Filled", Icons.Filled.VolunteerActivism),
        IconData("VpnKey", "Filled", Icons.Filled.VpnKey),
        IconData("VpnKeyOff", "Filled", Icons.Filled.VpnKeyOff),
        IconData("VpnLock", "Filled", Icons.Filled.VpnLock),
        IconData("Vrpano", "Filled", Icons.Filled.Vrpano),
        IconData("Wallet", "Filled", Icons.Filled.Wallet),
        IconData("Wallpaper", "Filled", Icons.Filled.Wallpaper),
        IconData("Warehouse", "Filled", Icons.Filled.Warehouse),
        IconData("WarningAmber", "Filled", Icons.Filled.WarningAmber),
        IconData("Wash", "Filled", Icons.Filled.Wash),
        IconData("Watch", "Filled", Icons.Filled.Watch),
        IconData("WatchLater", "Filled", Icons.Filled.WatchLater),
        IconData("WatchOff", "Filled", Icons.Filled.WatchOff),
        IconData("Water", "Filled", Icons.Filled.Water),
        IconData("WaterDamage", "Filled", Icons.Filled.WaterDamage),
        IconData("WaterDrop", "Filled", Icons.Filled.WaterDrop),
        IconData("WaterfallChart", "Filled", Icons.Filled.WaterfallChart),
        IconData("Waves", "Filled", Icons.Filled.Waves),
        IconData("WavingHand", "Filled", Icons.Filled.WavingHand),
        IconData("WbAuto", "Filled", Icons.Filled.WbAuto),
        IconData("WbCloudy", "Filled", Icons.Filled.WbCloudy),
        IconData("WbIncandescent", "Filled", Icons.Filled.WbIncandescent),
        IconData("WbIridescent", "Filled", Icons.Filled.WbIridescent),
        IconData("WbShade", "Filled", Icons.Filled.WbShade),
        IconData("WbSunny", "Filled", Icons.Filled.WbSunny),
        IconData("WbTwilight", "Filled", Icons.Filled.WbTwilight),
        IconData("Wc", "Filled", Icons.Filled.Wc),
        IconData("Web", "Filled", Icons.Filled.Web),
        IconData("WebAsset", "Filled", Icons.Filled.WebAsset),
        IconData("WebAssetOff", "Filled", Icons.Filled.WebAssetOff),
        IconData("WebStories", "Filled", Icons.Filled.WebStories),
        IconData("Webhook", "Filled", Icons.Filled.Webhook),
        IconData("Weekend", "Filled", Icons.Filled.Weekend),
        IconData("West", "Filled", Icons.Filled.West),
        IconData("Whatsapp", "Filled", Icons.Filled.Whatsapp),
        IconData("Whatshot", "Filled", Icons.Filled.Whatshot),
        IconData("WheelchairPickup", "Filled", Icons.Filled.WheelchairPickup),
        IconData("WhereToVote", "Filled", Icons.Filled.WhereToVote),
        IconData("Widgets", "Filled", Icons.Filled.Widgets),
        IconData("WidthFull", "Filled", Icons.Filled.WidthFull),
        IconData("WidthNormal", "Filled", Icons.Filled.WidthNormal),
        IconData("WidthWide", "Filled", Icons.Filled.WidthWide),
        IconData("Wifi", "Filled", Icons.Filled.Wifi),
        IconData("Wifi1Bar", "Filled", Icons.Filled.Wifi1Bar),
        IconData("Wifi2Bar", "Filled", Icons.Filled.Wifi2Bar),
        IconData("WifiCalling", "Filled", Icons.Filled.WifiCalling),
        IconData("WifiCalling3", "Filled", Icons.Filled.WifiCalling3),
        IconData("WifiChannel", "Filled", Icons.Filled.WifiChannel),
        IconData("WifiFind", "Filled", Icons.Filled.WifiFind),
        IconData("WifiLock", "Filled", Icons.Filled.WifiLock),
        IconData("WifiOff", "Filled", Icons.Filled.WifiOff),
        IconData("WifiPassword", "Filled", Icons.Filled.WifiPassword),
        IconData("WifiProtectedSetup", "Filled", Icons.Filled.WifiProtectedSetup),
        IconData("WifiTethering", "Filled", Icons.Filled.WifiTethering),
        IconData("WifiTetheringError", "Filled", Icons.Filled.WifiTetheringError),
        IconData("WifiTetheringErrorRounded", "Filled", Icons.Filled.WifiTetheringErrorRounded),
        IconData("WifiTetheringOff", "Filled", Icons.Filled.WifiTetheringOff),
        IconData("WindPower", "Filled", Icons.Filled.WindPower),
        IconData("Window", "Filled", Icons.Filled.Window),
        IconData("WineBar", "Filled", Icons.Filled.WineBar),
        IconData("Woman", "Filled", Icons.Filled.Woman),
        IconData("Woman2", "Filled", Icons.Filled.Woman2),
        IconData("Work", "Filled", Icons.Filled.Work),
        IconData("WorkHistory", "Filled", Icons.Filled.WorkHistory),
        IconData("WorkOff", "Filled", Icons.Filled.WorkOff),
        IconData("WorkOutline", "Filled", Icons.Filled.WorkOutline),
        IconData("WorkspacePremium", "Filled", Icons.Filled.WorkspacePremium),
        IconData("Workspaces", "Filled", Icons.Filled.Workspaces),
        IconData("WrongLocation", "Filled", Icons.Filled.WrongLocation),
        IconData("Yard", "Filled", Icons.Filled.Yard),
        IconData("YoutubeSearchedFor", "Filled", Icons.Filled.YoutubeSearchedFor),
        IconData("ZoomIn", "Filled", Icons.Filled.ZoomIn),
        IconData("ZoomInMap", "Filled", Icons.Filled.ZoomInMap),
        IconData("ZoomOut", "Filled", Icons.Filled.ZoomOut),
        IconData("ZoomOutMap", "Filled", Icons.Filled.ZoomOutMap),
        IconData("AccountBox", "Filled", Icons.Filled.AccountBox),
        IconData("AccountCircle", "Filled", Icons.Filled.AccountCircle),
        IconData("Add", "Filled", Icons.Filled.Add),
        IconData("AddCircle", "Filled", Icons.Filled.AddCircle),
        IconData("ArrowDropDown", "Filled", Icons.Filled.ArrowDropDown),
        IconData("Build", "Filled", Icons.Filled.Build),
        IconData("Call", "Filled", Icons.Filled.Call),
        IconData("Check", "Filled", Icons.Filled.Check),
        IconData("CheckCircle", "Filled", Icons.Filled.CheckCircle),
        IconData("Clear", "Filled", Icons.Filled.Clear),
        IconData("Close", "Filled", Icons.Filled.Close),
        IconData("Create", "Filled", Icons.Filled.Create),
        IconData("DateRange", "Filled", Icons.Filled.DateRange),
        IconData("Delete", "Filled", Icons.Filled.Delete),
        IconData("Done", "Filled", Icons.Filled.Done),
        IconData("Edit", "Filled", Icons.Filled.Edit),
        IconData("Email", "Filled", Icons.Filled.Email),
        IconData("Face", "Filled", Icons.Filled.Face),
        IconData("Favorite", "Filled", Icons.Filled.Favorite),
        IconData("FavoriteBorder", "Filled", Icons.Filled.FavoriteBorder),
        IconData("Home", "Filled", Icons.Filled.Home),
        IconData("Info", "Filled", Icons.Filled.Info),
        IconData("KeyboardArrowDown", "Filled", Icons.Filled.KeyboardArrowDown),
        IconData("KeyboardArrowUp", "Filled", Icons.Filled.KeyboardArrowUp),
        IconData("LocationOn", "Filled", Icons.Filled.LocationOn),
        IconData("Lock", "Filled", Icons.Filled.Lock),
        IconData("MailOutline", "Filled", Icons.Filled.MailOutline),
        IconData("Menu", "Filled", Icons.Filled.Menu),
        IconData("MoreVert", "Filled", Icons.Filled.MoreVert),
        IconData("Notifications", "Filled", Icons.Filled.Notifications),
        IconData("Person", "Filled", Icons.Filled.Person),
        IconData("Phone", "Filled", Icons.Filled.Phone),
        IconData("Place", "Filled", Icons.Filled.Place),
        IconData("PlayArrow", "Filled", Icons.Filled.PlayArrow),
        IconData("Refresh", "Filled", Icons.Filled.Refresh),
        IconData("Search", "Filled", Icons.Filled.Search),
        IconData("Settings", "Filled", Icons.Filled.Settings),
        IconData("Share", "Filled", Icons.Filled.Share),
        IconData("ShoppingCart", "Filled", Icons.Filled.ShoppingCart),
        IconData("Star", "Filled", Icons.Filled.Star),
        IconData("ThumbUp", "Filled", Icons.Filled.ThumbUp),
        IconData("Warning", "Filled", Icons.Filled.Warning),
        IconData("Accessible", "AutoMirroredFilled", Icons.AutoMirrored.Filled.Accessible),
        IconData("AccessibleForward", "AutoMirroredFilled", Icons.AutoMirrored.Filled.AccessibleForward),
        IconData("AddToHomeScreen", "AutoMirroredFilled", Icons.AutoMirrored.Filled.AddToHomeScreen),
        IconData("AirplaneTicket", "AutoMirroredFilled", Icons.AutoMirrored.Filled.AirplaneTicket),
        IconData("AlignHorizontalLeft", "AutoMirroredFilled", Icons.AutoMirrored.Filled.AlignHorizontalLeft),
        IconData("AlignHorizontalRight", "AutoMirroredFilled", Icons.AutoMirrored.Filled.AlignHorizontalRight),
        IconData("AltRoute", "AutoMirroredFilled", Icons.AutoMirrored.Filled.AltRoute),
        IconData("Announcement", "AutoMirroredFilled", Icons.AutoMirrored.Filled.Announcement),
        IconData("ArrowBackIos", "AutoMirroredFilled", Icons.AutoMirrored.Filled.ArrowBackIos),
        IconData("ArrowForwardIos", "AutoMirroredFilled", Icons.AutoMirrored.Filled.ArrowForwardIos),
        IconData("ArrowLeft", "AutoMirroredFilled", Icons.AutoMirrored.Filled.ArrowLeft),
        IconData("ArrowRight", "AutoMirroredFilled", Icons.AutoMirrored.Filled.ArrowRight),
        IconData("ArrowRightAlt", "AutoMirroredFilled", Icons.AutoMirrored.Filled.ArrowRightAlt),
        IconData("Article", "AutoMirroredFilled", Icons.AutoMirrored.Filled.Article),
        IconData("Assignment", "AutoMirroredFilled", Icons.AutoMirrored.Filled.Assignment),
        IconData("AssignmentReturn", "AutoMirroredFilled", Icons.AutoMirrored.Filled.AssignmentReturn),
        IconData("AssistantDirection", "AutoMirroredFilled", Icons.AutoMirrored.Filled.AssistantDirection),
        IconData("Backspace", "AutoMirroredFilled", Icons.AutoMirrored.Filled.Backspace),
        IconData("BatteryUnknown", "AutoMirroredFilled", Icons.AutoMirrored.Filled.BatteryUnknown),
        IconData("BluetoothSearching", "AutoMirroredFilled", Icons.AutoMirrored.Filled.BluetoothSearching),
        IconData("BrandingWatermark", "AutoMirroredFilled", Icons.AutoMirrored.Filled.BrandingWatermark),
        IconData("CallMade", "AutoMirroredFilled", Icons.AutoMirrored.Filled.CallMade),
        IconData("CallMerge", "AutoMirroredFilled", Icons.AutoMirrored.Filled.CallMerge),
        IconData("CallMissed", "AutoMirroredFilled", Icons.AutoMirrored.Filled.CallMissed),
        IconData("CallMissedOutgoing", "AutoMirroredFilled", Icons.AutoMirrored.Filled.CallMissedOutgoing),
        IconData("CallReceived", "AutoMirroredFilled", Icons.AutoMirrored.Filled.CallReceived),
        IconData("CallSplit", "AutoMirroredFilled", Icons.AutoMirrored.Filled.CallSplit),
        IconData("Chat", "AutoMirroredFilled", Icons.AutoMirrored.Filled.Chat),
        IconData("ChromeReaderMode", "AutoMirroredFilled", Icons.AutoMirrored.Filled.ChromeReaderMode),
        IconData("Comment", "AutoMirroredFilled", Icons.AutoMirrored.Filled.Comment),
        IconData("CompareArrows", "AutoMirroredFilled", Icons.AutoMirrored.Filled.CompareArrows),
        IconData("ContactSupport", "AutoMirroredFilled", Icons.AutoMirrored.Filled.ContactSupport),
        IconData("DirectionsBike", "AutoMirroredFilled", Icons.AutoMirrored.Filled.DirectionsBike),
        IconData("DirectionsRun", "AutoMirroredFilled", Icons.AutoMirrored.Filled.DirectionsRun),
        IconData("DirectionsWalk", "AutoMirroredFilled", Icons.AutoMirrored.Filled.DirectionsWalk),
        IconData("DriveFileMove", "AutoMirroredFilled", Icons.AutoMirrored.Filled.DriveFileMove),
        IconData("Dvr", "AutoMirroredFilled", Icons.AutoMirrored.Filled.Dvr),
        IconData("EventNote", "AutoMirroredFilled", Icons.AutoMirrored.Filled.EventNote),
        IconData("FactCheck", "AutoMirroredFilled", Icons.AutoMirrored.Filled.FactCheck),
        IconData("FeaturedPlayList", "AutoMirroredFilled", Icons.AutoMirrored.Filled.FeaturedPlayList),
        IconData("FeaturedVideo", "AutoMirroredFilled", Icons.AutoMirrored.Filled.FeaturedVideo),
        IconData("Feed", "AutoMirroredFilled", Icons.AutoMirrored.Filled.Feed),
        IconData("FollowTheSigns", "AutoMirroredFilled", Icons.AutoMirrored.Filled.FollowTheSigns),
        IconData("FormatAlignLeft", "AutoMirroredFilled", Icons.AutoMirrored.Filled.FormatAlignLeft),
        IconData("FormatAlignRight", "AutoMirroredFilled", Icons.AutoMirrored.Filled.FormatAlignRight),
        IconData("FormatIndentDecrease", "AutoMirroredFilled", Icons.AutoMirrored.Filled.FormatIndentDecrease),
        IconData("FormatIndentIncrease", "AutoMirroredFilled", Icons.AutoMirrored.Filled.FormatIndentIncrease),
        IconData("FormatListBulleted", "AutoMirroredFilled", Icons.AutoMirrored.Filled.FormatListBulleted),
        IconData("FormatTextdirectionLToR", "AutoMirroredFilled", Icons.AutoMirrored.Filled.FormatTextdirectionLToR),
        IconData("FormatTextdirectionRToL", "AutoMirroredFilled", Icons.AutoMirrored.Filled.FormatTextdirectionRToL),
        IconData("Forward", "AutoMirroredFilled", Icons.AutoMirrored.Filled.Forward),
        IconData("ForwardToInbox", "AutoMirroredFilled", Icons.AutoMirrored.Filled.ForwardToInbox),
        IconData("Grading", "AutoMirroredFilled", Icons.AutoMirrored.Filled.Grading),
        IconData("Help", "AutoMirroredFilled", Icons.AutoMirrored.Filled.Help),
        IconData("HelpCenter", "AutoMirroredFilled", Icons.AutoMirrored.Filled.HelpCenter),
        IconData("HelpOutline", "AutoMirroredFilled", Icons.AutoMirrored.Filled.HelpOutline),
        IconData("Input", "AutoMirroredFilled", Icons.AutoMirrored.Filled.Input),
        IconData("InsertComment", "AutoMirroredFilled", Icons.AutoMirrored.Filled.InsertComment),
        IconData("InsertDriveFile", "AutoMirroredFilled", Icons.AutoMirrored.Filled.InsertDriveFile),
        IconData("KeyboardBackspace", "AutoMirroredFilled", Icons.AutoMirrored.Filled.KeyboardBackspace),
        IconData("KeyboardReturn", "AutoMirroredFilled", Icons.AutoMirrored.Filled.KeyboardReturn),
        IconData("KeyboardTab", "AutoMirroredFilled", Icons.AutoMirrored.Filled.KeyboardTab),
        IconData("Label", "AutoMirroredFilled", Icons.AutoMirrored.Filled.Label),
        IconData("LabelImportant", "AutoMirroredFilled", Icons.AutoMirrored.Filled.LabelImportant),
        IconData("LabelOff", "AutoMirroredFilled", Icons.AutoMirrored.Filled.LabelOff),
        IconData("LastPage", "AutoMirroredFilled", Icons.AutoMirrored.Filled.LastPage),
        IconData("Launch", "AutoMirroredFilled", Icons.AutoMirrored.Filled.Launch),
        IconData("LibraryBooks", "AutoMirroredFilled", Icons.AutoMirrored.Filled.LibraryBooks),
        IconData("ListAlt", "AutoMirroredFilled", Icons.AutoMirrored.Filled.ListAlt),
        IconData("LiveHelp", "AutoMirroredFilled", Icons.AutoMirrored.Filled.LiveHelp),
        IconData("Login", "AutoMirroredFilled", Icons.AutoMirrored.Filled.Login),
        IconData("Logout", "AutoMirroredFilled", Icons.AutoMirrored.Filled.Logout),
        IconData("ManageSearch", "AutoMirroredFilled", Icons.AutoMirrored.Filled.ManageSearch),
        IconData("MenuBook", "AutoMirroredFilled", Icons.AutoMirrored.Filled.MenuBook),
        IconData("MenuOpen", "AutoMirroredFilled", Icons.AutoMirrored.Filled.MenuOpen),
        IconData("MergeType", "AutoMirroredFilled", Icons.AutoMirrored.Filled.MergeType),
        IconData("Message", "AutoMirroredFilled", Icons.AutoMirrored.Filled.Message),
        IconData("MissedVideoCall", "AutoMirroredFilled", Icons.AutoMirrored.Filled.MissedVideoCall),
        IconData("MobileScreenShare", "AutoMirroredFilled", Icons.AutoMirrored.Filled.MobileScreenShare),
        IconData("More", "AutoMirroredFilled", Icons.AutoMirrored.Filled.More),
        IconData("MultilineChart", "AutoMirroredFilled", Icons.AutoMirrored.Filled.MultilineChart),
        IconData("NavigateBefore", "AutoMirroredFilled", Icons.AutoMirrored.Filled.NavigateBefore),
        IconData("NavigateNext", "AutoMirroredFilled", Icons.AutoMirrored.Filled.NavigateNext),
        IconData("NextPlan", "AutoMirroredFilled", Icons.AutoMirrored.Filled.NextPlan),
        IconData("NextWeek", "AutoMirroredFilled", Icons.AutoMirrored.Filled.NextWeek),
        IconData("NotListedLocation", "AutoMirroredFilled", Icons.AutoMirrored.Filled.NotListedLocation),
        IconData("Note", "AutoMirroredFilled", Icons.AutoMirrored.Filled.Note),
        IconData("NoteAdd", "AutoMirroredFilled", Icons.AutoMirrored.Filled.NoteAdd),
        IconData("Notes", "AutoMirroredFilled", Icons.AutoMirrored.Filled.Notes),
        IconData("OfflineShare", "AutoMirroredFilled", Icons.AutoMirrored.Filled.OfflineShare),
        IconData("OpenInNew", "AutoMirroredFilled", Icons.AutoMirrored.Filled.OpenInNew),
        IconData("Outbound", "AutoMirroredFilled", Icons.AutoMirrored.Filled.Outbound),
        IconData("PhoneCallback", "AutoMirroredFilled", Icons.AutoMirrored.Filled.PhoneCallback),
        IconData("PhoneForwarded", "AutoMirroredFilled", Icons.AutoMirrored.Filled.PhoneForwarded),
        IconData("PhoneMissed", "AutoMirroredFilled", Icons.AutoMirrored.Filled.PhoneMissed),
        IconData("PlaylistAdd", "AutoMirroredFilled", Icons.AutoMirrored.Filled.PlaylistAdd),
        IconData("PlaylistAddCheck", "AutoMirroredFilled", Icons.AutoMirrored.Filled.PlaylistAddCheck),
        IconData("PlaylistPlay", "AutoMirroredFilled", Icons.AutoMirrored.Filled.PlaylistPlay),
        IconData("QueueMusic", "AutoMirroredFilled", Icons.AutoMirrored.Filled.QueueMusic),
        IconData("ReadMore", "AutoMirroredFilled", Icons.AutoMirrored.Filled.ReadMore),
        IconData("ReceiptLong", "AutoMirroredFilled", Icons.AutoMirrored.Filled.ReceiptLong),
        IconData("Redo", "AutoMirroredFilled", Icons.AutoMirrored.Filled.Redo),
        IconData("Reply", "AutoMirroredFilled", Icons.AutoMirrored.Filled.Reply),
        IconData("ReplyAll", "AutoMirroredFilled", Icons.AutoMirrored.Filled.ReplyAll),
        IconData("RotateLeft", "AutoMirroredFilled", Icons.AutoMirrored.Filled.RotateLeft),
        IconData("RotateRight", "AutoMirroredFilled", Icons.AutoMirrored.Filled.RotateRight),
        IconData("Rtt", "AutoMirroredFilled", Icons.AutoMirrored.Filled.Rtt),
        IconData("Rule", "AutoMirroredFilled", Icons.AutoMirrored.Filled.Rule),
        IconData("ScheduleSend", "AutoMirroredFilled", Icons.AutoMirrored.Filled.ScheduleSend),
        IconData("ScreenShare", "AutoMirroredFilled", Icons.AutoMirrored.Filled.ScreenShare),
        IconData("Segment", "AutoMirroredFilled", Icons.AutoMirrored.Filled.Segment),
        IconData("SendAndArchive", "AutoMirroredFilled", Icons.AutoMirrored.Filled.SendAndArchive),
        IconData("SendToMobile", "AutoMirroredFilled", Icons.AutoMirrored.Filled.SendToMobile),
        IconData("ShortText", "AutoMirroredFilled", Icons.AutoMirrored.Filled.ShortText),
        IconData("Shortcut", "AutoMirroredFilled", Icons.AutoMirrored.Filled.Shortcut),
        IconData("ShowChart", "AutoMirroredFilled", Icons.AutoMirrored.Filled.ShowChart),
        IconData("Sort", "AutoMirroredFilled", Icons.AutoMirrored.Filled.Sort),
        IconData("SpeakerNotes", "AutoMirroredFilled", Icons.AutoMirrored.Filled.SpeakerNotes),
        IconData("StarHalf", "AutoMirroredFilled", Icons.AutoMirrored.Filled.StarHalf),
        IconData("StickyNote2", "AutoMirroredFilled", Icons.AutoMirrored.Filled.StickyNote2),
        IconData("StopScreenShare", "AutoMirroredFilled", Icons.AutoMirrored.Filled.StopScreenShare),
        IconData("Subject", "AutoMirroredFilled", Icons.AutoMirrored.Filled.Subject),
        IconData("TextSnippet", "AutoMirroredFilled", Icons.AutoMirrored.Filled.TextSnippet),
        IconData("Toc", "AutoMirroredFilled", Icons.AutoMirrored.Filled.Toc),
        IconData("TrendingDown", "AutoMirroredFilled", Icons.AutoMirrored.Filled.TrendingDown),
        IconData("TrendingFlat", "AutoMirroredFilled", Icons.AutoMirrored.Filled.TrendingFlat),
        IconData("TrendingUp", "AutoMirroredFilled", Icons.AutoMirrored.Filled.TrendingUp),
        IconData("Undo", "AutoMirroredFilled", Icons.AutoMirrored.Filled.Undo),
        IconData("ViewList", "AutoMirroredFilled", Icons.AutoMirrored.Filled.ViewList),
        IconData("ViewQuilt", "AutoMirroredFilled", Icons.AutoMirrored.Filled.ViewQuilt),
        IconData("ViewSidebar", "AutoMirroredFilled", Icons.AutoMirrored.Filled.ViewSidebar),
        IconData("VolumeDown", "AutoMirroredFilled", Icons.AutoMirrored.Filled.VolumeDown),
        IconData("VolumeMute", "AutoMirroredFilled", Icons.AutoMirrored.Filled.VolumeMute),
        IconData("VolumeOff", "AutoMirroredFilled", Icons.AutoMirrored.Filled.VolumeOff),
        IconData("VolumeUp", "AutoMirroredFilled", Icons.AutoMirrored.Filled.VolumeUp),
        IconData("WrapText", "AutoMirroredFilled", Icons.AutoMirrored.Filled.WrapText),
        IconData("Wysiwyg", "AutoMirroredFilled", Icons.AutoMirrored.Filled.Wysiwyg),
        IconData("ArrowBack", "AutoMirroredFilled", Icons.AutoMirrored.Filled.ArrowBack),
        IconData("ArrowForward", "AutoMirroredFilled", Icons.AutoMirrored.Filled.ArrowForward),
        IconData("ExitToApp", "AutoMirroredFilled", Icons.AutoMirrored.Filled.ExitToApp),
        IconData("KeyboardArrowLeft", "AutoMirroredFilled", Icons.AutoMirrored.Filled.KeyboardArrowLeft),
        IconData("KeyboardArrowRight", "AutoMirroredFilled", Icons.AutoMirrored.Filled.KeyboardArrowRight),
        IconData("List", "AutoMirroredFilled", Icons.AutoMirrored.Filled.List),
        IconData("Send", "AutoMirroredFilled", Icons.AutoMirrored.Filled.Send),
        IconData("Accessible", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.Accessible),
        IconData("AccessibleForward", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.AccessibleForward),
        IconData("AddToHomeScreen", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.AddToHomeScreen),
        IconData("AirplaneTicket", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.AirplaneTicket),
        IconData("AlignHorizontalLeft", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.AlignHorizontalLeft),
        IconData("AlignHorizontalRight", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.AlignHorizontalRight),
        IconData("AltRoute", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.AltRoute),
        IconData("Announcement", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.Announcement),
        IconData("ArrowBackIos", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.ArrowBackIos),
        IconData("ArrowForwardIos", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.ArrowForwardIos),
        IconData("ArrowLeft", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.ArrowLeft),
        IconData("ArrowRight", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.ArrowRight),
        IconData("ArrowRightAlt", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.ArrowRightAlt),
        IconData("Article", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.Article),
        IconData("Assignment", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.Assignment),
        IconData("AssignmentReturn", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.AssignmentReturn),
        IconData("AssistantDirection", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.AssistantDirection),
        IconData("Backspace", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.Backspace),
        IconData("BatteryUnknown", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.BatteryUnknown),
        IconData("BluetoothSearching", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.BluetoothSearching),
        IconData("BrandingWatermark", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.BrandingWatermark),
        IconData("CallMade", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.CallMade),
        IconData("CallMerge", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.CallMerge),
        IconData("CallMissed", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.CallMissed),
        IconData("CallMissedOutgoing", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.CallMissedOutgoing),
        IconData("CallReceived", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.CallReceived),
        IconData("CallSplit", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.CallSplit),
        IconData("Chat", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.Chat),
        IconData("ChromeReaderMode", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.ChromeReaderMode),
        IconData("Comment", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.Comment),
        IconData("CompareArrows", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.CompareArrows),
        IconData("ContactSupport", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.ContactSupport),
        IconData("DirectionsBike", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.DirectionsBike),
        IconData("DirectionsRun", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.DirectionsRun),
        IconData("DirectionsWalk", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.DirectionsWalk),
        IconData("DriveFileMove", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.DriveFileMove),
        IconData("Dvr", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.Dvr),
        IconData("EventNote", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.EventNote),
        IconData("FactCheck", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.FactCheck),
        IconData("FeaturedPlayList", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.FeaturedPlayList),
        IconData("FeaturedVideo", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.FeaturedVideo),
        IconData("Feed", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.Feed),
        IconData("FollowTheSigns", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.FollowTheSigns),
        IconData("FormatAlignLeft", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.FormatAlignLeft),
        IconData("FormatAlignRight", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.FormatAlignRight),
        IconData("FormatIndentDecrease", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.FormatIndentDecrease),
        IconData("FormatIndentIncrease", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.FormatIndentIncrease),
        IconData("FormatListBulleted", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.FormatListBulleted),
        IconData("FormatTextdirectionLToR", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.FormatTextdirectionLToR),
        IconData("FormatTextdirectionRToL", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.FormatTextdirectionRToL),
        IconData("Forward", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.Forward),
        IconData("ForwardToInbox", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.ForwardToInbox),
        IconData("Grading", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.Grading),
        IconData("Help", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.Help),
        IconData("HelpCenter", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.HelpCenter),
        IconData("HelpOutline", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.HelpOutline),
        IconData("Input", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.Input),
        IconData("InsertComment", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.InsertComment),
        IconData("InsertDriveFile", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.InsertDriveFile),
        IconData("KeyboardBackspace", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.KeyboardBackspace),
        IconData("KeyboardReturn", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.KeyboardReturn),
        IconData("KeyboardTab", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.KeyboardTab),
        IconData("Label", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.Label),
        IconData("LabelImportant", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.LabelImportant),
        IconData("LabelOff", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.LabelOff),
        IconData("LastPage", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.LastPage),
        IconData("Launch", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.Launch),
        IconData("LibraryBooks", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.LibraryBooks),
        IconData("ListAlt", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.ListAlt),
        IconData("LiveHelp", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.LiveHelp),
        IconData("Login", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.Login),
        IconData("Logout", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.Logout),
        IconData("ManageSearch", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.ManageSearch),
        IconData("MenuBook", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.MenuBook),
        IconData("MenuOpen", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.MenuOpen),
        IconData("MergeType", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.MergeType),
        IconData("Message", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.Message),
        IconData("MissedVideoCall", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.MissedVideoCall),
        IconData("MobileScreenShare", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.MobileScreenShare),
        IconData("More", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.More),
        IconData("MultilineChart", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.MultilineChart),
        IconData("NavigateBefore", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.NavigateBefore),
        IconData("NavigateNext", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.NavigateNext),
        IconData("NextPlan", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.NextPlan),
        IconData("NextWeek", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.NextWeek),
        IconData("NotListedLocation", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.NotListedLocation),
        IconData("Note", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.Note),
        IconData("NoteAdd", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.NoteAdd),
        IconData("Notes", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.Notes),
        IconData("OfflineShare", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.OfflineShare),
        IconData("OpenInNew", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.OpenInNew),
        IconData("Outbound", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.Outbound),
        IconData("PhoneCallback", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.PhoneCallback),
        IconData("PhoneForwarded", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.PhoneForwarded),
        IconData("PhoneMissed", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.PhoneMissed),
        IconData("PlaylistAdd", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.PlaylistAdd),
        IconData("PlaylistAddCheck", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.PlaylistAddCheck),
        IconData("PlaylistPlay", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.PlaylistPlay),
        IconData("QueueMusic", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.QueueMusic),
        IconData("ReadMore", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.ReadMore),
        IconData("ReceiptLong", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.ReceiptLong),
        IconData("Redo", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.Redo),
        IconData("Reply", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.Reply),
        IconData("ReplyAll", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.ReplyAll),
        IconData("RotateLeft", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.RotateLeft),
        IconData("RotateRight", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.RotateRight),
        IconData("Rtt", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.Rtt),
        IconData("Rule", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.Rule),
        IconData("ScheduleSend", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.ScheduleSend),
        IconData("ScreenShare", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.ScreenShare),
        IconData("Segment", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.Segment),
        IconData("SendAndArchive", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.SendAndArchive),
        IconData("SendToMobile", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.SendToMobile),
        IconData("ShortText", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.ShortText),
        IconData("Shortcut", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.Shortcut),
        IconData("ShowChart", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.ShowChart),
        IconData("Sort", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.Sort),
        IconData("SpeakerNotes", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.SpeakerNotes),
        IconData("StarHalf", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.StarHalf),
        IconData("StickyNote2", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.StickyNote2),
        IconData("StopScreenShare", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.StopScreenShare),
        IconData("Subject", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.Subject),
        IconData("TextSnippet", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.TextSnippet),
        IconData("Toc", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.Toc),
        IconData("TrendingDown", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.TrendingDown),
        IconData("TrendingFlat", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.TrendingFlat),
        IconData("TrendingUp", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.TrendingUp),
        IconData("Undo", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.Undo),
        IconData("ViewList", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.ViewList),
        IconData("ViewQuilt", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.ViewQuilt),
        IconData("ViewSidebar", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.ViewSidebar),
        IconData("VolumeDown", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.VolumeDown),
        IconData("VolumeMute", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.VolumeMute),
        IconData("VolumeOff", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.VolumeOff),
        IconData("VolumeUp", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.VolumeUp),
        IconData("WrapText", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.WrapText),
        IconData("Wysiwyg", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.Wysiwyg),
        IconData("ArrowBack", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.ArrowBack),
        IconData("ArrowForward", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.ArrowForward),
        IconData("ExitToApp", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.ExitToApp),
        IconData("KeyboardArrowLeft", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.KeyboardArrowLeft),
        IconData("KeyboardArrowRight", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.KeyboardArrowRight),
        IconData("List", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.List),
        IconData("Send", "AutoMirroredOutlined", Icons.AutoMirrored.Outlined.Send)
    )



    /**
     * 获取指定类型的所有图标名称
     * @param type 图标类型
     * @return 该类型下的所有图标名称列表
     */
    fun getByType(type: String): List<String> {
        return allIcons
            .filter { it.iconType == type }
            .map { it.iconKey }
            .toList()
    }

    /**
     * 获取所有图标类型
     * @return 图标类型列表
     */
    fun getAllTypes(): List<String> {
        return allIcons
            .map { it.iconType }
            .distinct()
            .toList()
    }



    /**
     * 根据类型和名称获取图标
     * @param type 图标类型
     * @param name 图标名称
     * @return 找到的图标，如果不存在则返回null
     */
    @Composable
    fun getByName(type: String, name: String): ImageVector? {
        return allIcons
            .filter { it.iconType == type && it.iconKey == name }
            .map { it.vector }
            .firstOrNull()
    }

    /**
     * 根据类型和名称获取图标，如果不存在则返回默认图标
     * @param type 图标类型
     * @param name 图标名称
     * @param default 默认图标提供函数
     * @return 找到的图标或默认图标
     */
    @Composable
    fun getByNameOrDefault(type: String, name: String, default: @Composable () -> ImageVector): ImageVector {
        return getByName(type, name) ?: default()
    }
    
    /**
     * 通过图标名称获取图标数据
     * @param iconKey 图标名称
     * @return 图标数据对象IconData
     */
    @Composable
    operator fun get(iconKey: String): IconData {
        return allIcons
            .filter { it.iconKey == iconKey }
            .firstOrNull() 
            ?: allIcons
                .filter { it.iconType == "Filled" }
                .firstOrNull()
                ?: IconData("Info", "Filled", Icons.Filled.Info)
    }

}
