package com.addzero.kmp.component.form.number

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.AttachMoney
import androidx.compose.material.icons.filled.Numbers
import androidx.compose.material.icons.filled.Percent
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.input.KeyboardType
import com.addzero.kmp.component.form.AddTextField
import com.addzero.kmp.component.form.RemoteValidationConfig
import com.addzero.kmp.enums.RegexEnum

/**
 * 整数输入框
 * 基于 AddTextField 的整数专用输入组件
 *
 * @param value 当前值
 * @param onValueChange 值变化回调
 * @param label 输入框标签
 * @param placeholder 占位符文本
 * @param isRequired 是否必填
 * @param modifier 修饰符
 * @param maxLength 最大长度限制
 * @param onValidate 验证结果回调
 * @param leadingIcon 前置图标
 * @param disable 是否禁用
 * @param supportingText 支持文本
 * @param trailingIcon 后置图标
 * @param onErrMsgChange 错误信息变化回调
 * @param errorMessages 外部错误信息
 * @param remoteValidationConfig 远程验证配置
 * @param allowNegative 是否允许负数，默认true
 * @param minValue 最小值限制
 * @param maxValue 最大值限制
 */
@Composable
fun AddIntegerField(
    value: String,
    onValueChange: (String) -> Unit,
    label: String = "",
    placeholder: String = "请输入整数",
    isRequired: Boolean = true,
    modifier: Modifier = Modifier,
    maxLength: Int? = null,
    onValidate: ((Boolean) -> Unit)? = null,
    leadingIcon: ImageVector? = Icons.Default.Numbers,
    disable: Boolean = false,
    supportingText: @Composable (() -> Unit)? = null,
    trailingIcon: @Composable (() -> Unit)? = null,
    onErrMsgChange: ((String, String) -> Unit)? = null,
    errorMessages: List<String> = emptyList(),
    remoteValidationConfig: RemoteValidationConfig? = null,
    allowNegative: Boolean = true,
    minValue: Long? = null,
    maxValue: Long? = null
) {
    // 选择合适的正则验证器
    val regexValidator = if (allowNegative) RegexEnum.INTEGER else RegexEnum.POSITIVE_INTEGER
    
    // 数值范围验证器
    val rangeValidators = remember(minValue, maxValue) {
        if (minValue != null || maxValue != null) {
            listOf({ input: String ->
                if (input.isBlank()) true
                else {
                    val longValue = input.toLongOrNull()
                    when {
                        longValue == null -> false
                        minValue != null && longValue < minValue -> false
                        maxValue != null && longValue > maxValue -> false
                        else -> true
                    }
                }
            } to buildString {
                append("数值必须在")
                if (minValue != null) append("$minValue")
                if (minValue != null && maxValue != null) append("到")
                if (maxValue != null) append("$maxValue")
                append("之间")
            })
        } else {
            emptyList()
        }
    }

    AddTextField(
        value = value,
        onValueChange = onValueChange,
        label = label,
        placeholder = placeholder,
        isRequired = isRequired,
        regexValidator = regexValidator,
        validators = rangeValidators,
        modifier = modifier,
        maxLength = maxLength,
        onValidate = onValidate,
        leadingIcon = leadingIcon,
        disable = disable,
        supportingText = supportingText,
        trailingIcon = trailingIcon,
        keyboardType = KeyboardType.Number,
        onErrMsgChange = onErrMsgChange,
        errorMessages = errorMessages,
        remoteValidationConfig = remoteValidationConfig
    )
}

/**
 * 小数输入框
 * 基于 AddTextField 的小数专用输入组件
 *
 * @param value 当前值
 * @param onValueChange 值变化回调
 * @param label 输入框标签
 * @param placeholder 占位符文本
 * @param isRequired 是否必填
 * @param modifier 修饰符
 * @param maxLength 最大长度限制
 * @param onValidate 验证结果回调
 * @param leadingIcon 前置图标
 * @param disable 是否禁用
 * @param supportingText 支持文本
 * @param trailingIcon 后置图标
 * @param onErrMsgChange 错误信息变化回调
 * @param errorMessages 外部错误信息
 * @param remoteValidationConfig 远程验证配置
 * @param decimalPlaces 小数位数限制，0表示不限制，默认2位
 * @param minValue 最小值限制
 * @param maxValue 最大值限制
 */
@Composable
fun AddDecimalField(
    value: String,
    onValueChange: (String) -> Unit,
    label: String = "",
    placeholder: String = "请输入小数",
    isRequired: Boolean = true,
    modifier: Modifier = Modifier,
    maxLength: Int? = null,
    onValidate: ((Boolean) -> Unit)? = null,
    leadingIcon: ImageVector? = Icons.Default.Numbers,
    disable: Boolean = false,
    supportingText: @Composable (() -> Unit)? = null,
    trailingIcon: @Composable (() -> Unit)? = null,
    onErrMsgChange: ((String, String) -> Unit)? = null,
    errorMessages: List<String> = emptyList(),
    remoteValidationConfig: RemoteValidationConfig? = null,
    decimalPlaces: Int = 2,
    minValue: Double? = null,
    maxValue: Double? = null
) {
    // 小数验证器
    val decimalValidators = remember(decimalPlaces, minValue, maxValue) {
        mutableListOf<Pair<(String) -> Boolean, String>>().apply {
            // 小数格式验证（包含小数位数限制）
            add({ input ->
                if (input.isBlank()) true
                else {
                    val regex = if (decimalPlaces > 0) {
                        "^-?\\d*(\\.\\d{0,$decimalPlaces})?$"
                    } else {
                        "^-?\\d*\\.?\\d*$"
                    }
                    input.matches(Regex(regex))
                }
            } to if (decimalPlaces > 0) {
                "请输入有效的小数，最多${decimalPlaces}位小数"
            } else {
                "请输入有效的小数"
            })
            
            // 数值范围验证
            if (minValue != null || maxValue != null) {
                add({ input ->
                    if (input.isBlank()) true
                    else {
                        val doubleValue = input.toDoubleOrNull()
                        when {
                            doubleValue == null -> false
                            minValue != null && doubleValue < minValue -> false
                            maxValue != null && doubleValue > maxValue -> false
                            else -> true
                        }
                    }
                } to buildString {
                    append("数值必须在")
                    if (minValue != null) append("$minValue")
                    if (minValue != null && maxValue != null) append("到")
                    if (maxValue != null) append("$maxValue")
                    append("之间")
                })
            }
        }
    }

    AddTextField(
        value = value,
        onValueChange = onValueChange,
        label = label,
        placeholder = placeholder,
        isRequired = isRequired,
        validators = decimalValidators,
        modifier = modifier,
        maxLength = maxLength,
        onValidate = onValidate,
        leadingIcon = leadingIcon,
        disable = disable,
        supportingText = supportingText,
        trailingIcon = trailingIcon,
        keyboardType = KeyboardType.Decimal,
        onErrMsgChange = onErrMsgChange,
        errorMessages = errorMessages,
        remoteValidationConfig = remoteValidationConfig
    )
}
