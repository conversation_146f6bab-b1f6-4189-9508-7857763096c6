package com.addzero.kmp.mock

import com.addzero.kmp.jdbc.meta.public.table.impl.ISysUserImpl

val sampleSysUserVos = listOf(
    ISysUserImpl(
        id = 1L,
        phone = "13800138000",
        password = "password123",
        avatar = "https://i.loli.net/2019/11/10/T7Mu8Aod3egmC4Q.png",
        nickname = "张三丰",
        gender = "1",
        username = "z<PERSON><PERSON><PERSON>",
        email = ""
    ),
    ISysUserImpl(
        id = 2L,
        phone = "13800138001",
        password = "password234",
        avatar = "https://i.loli.net/2019/11/10/T7Mu8Aod3egmC4Q.png",
        nickname = "李四",
        gender = "1",
        username = "lisi",
        email = ""
    ),
    ISysUserImpl(
        id = 3L,
        phone = "13800138002",
        password = "password345",
        avatar = "https://i.loli.net/2019/11/10/T7Mu8Aod3egmC4Q.png",
        nickname = "王五",
        gender = "1",
        username = "wangwu",
        email = ""
    ),
    ISysUserImpl(
        id = 4L,
        phone = "13800138003",
        password = "password456",
        avatar = "https://i.loli.net/2019/11/10/T7Mu8Aod3egmC4Q.png",
        nickname = "赵六",
        gender = "1",
        username = "zhaoliu",
        email = ""
    ),
    ISysUserImpl(
        id = 5L,
        phone = "13800138004",
        password = "password567",
        avatar = "https://i.loli.net/2019/11/10/T7Mu8Aod3egmC4Q.png",
        nickname = "钱七",
        gender = "1",
        username = "qianqi",
        email = ""
    ),
    ISysUserImpl(
        id = 6L,
        phone = "13800138005",
        password = "password678",
        avatar = "https://i.loli.net/2019/11/10/T7Mu8Aod3egmC4Q.png",
        nickname = "孙八",
        gender = "1",
        username = "sunba",
        email = ""
    ),
    ISysUserImpl(
        id = 7L,
        phone = "13800138006",
        password = "password789",
        avatar = "https://i.loli.net/2019/11/10/T7Mu8Aod3egmC4Q.png",
        nickname = "周九",
        gender = "1",
        username = "zhoujiu",
        email = ""
    ),
    ISysUserImpl(
        id = 8L,
        phone = "13800138007",
        password = "password890",
        avatar = "https://i.loli.net/2019/11/10/T7Mu8Aod3egmC4Q.png",
        nickname = "吴十",
        gender = "1",
        username = "wushi",
        email = ""
    ),
    ISysUserImpl(
        id = 9L,
        phone = "13800138008",
        password = "password901",
        avatar = "https://i.loli.net/2019/11/10/T7Mu8Aod3egmC4Q.png",
        nickname = "郑十一",
        gender = "1",
        username = "zhengshiyi",
        email = ""
    ),
    ISysUserImpl(
        id = 10L,
        phone = "13800138009",
        password = "password012",
        avatar = "https://i.loli.net/2019/11/10/T7Mu8Aod3egmC4Q.png",
        nickname = "刘十二",
        gender = "1",
        username = "liushier",
        email = ""
    )
)
