
package com.addzero.kmp.compose.icons

/**
 * 图标类型常量类
 * 为IDE提供自动完成提示支持
 */
object IconTypeKeys {
    /**
     * 默认图标类型
     */
    const val DEFAULT = "Default" 
    /**
     * 填充图标类型
     */
    const val FILLED = "Filled" 
    /**
     * 轮廓图标类型
     */
    const val OUTLINED = "Outlined" 
    /**
     * 圆角图标类型
     */
    const val ROUNDED = "Rounded" 
    /**
     * 尖角图标类型
     */
    const val SHARP = "Sharp" 
    /**
     * 双色调图标类型
     */
    const val TWOTONE = "TwoTone" 
    /**
     * 自动镜像填充图标类型
     */
    const val AUTOMIRROREDFILLED = "AutoMirroredFilled" 
    /**
     * 自动镜像轮廓图标类型
     */
    const val AUTOMIRROREDOUTLINED = "AutoMirroredOutlined" 
    /**
     * 自动镜像圆角图标类型
     */
    const val AUTOMIRROREDROUNDED = "AutoMirroredRounded" 
    /**
     * 自动镜像尖角图标类型
     */
    const val AUTOMIRROREDSHARP = "AutoMirroredSharp" 
    /**
     * 自动镜像双色调图标类型
     */
    const val AUTOMIRROREDTWOTONE = "AutoMirroredTwoTone" 
}
