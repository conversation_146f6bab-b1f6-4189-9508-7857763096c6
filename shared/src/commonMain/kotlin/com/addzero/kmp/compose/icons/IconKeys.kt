
package com.addzero.kmp.compose.icons

/**
 * 自动生成的图标键常量
 */
object IconKeys {
    // 图标名称常量
    const val ABC = "Abc"
    const val ACUNIT = "AcUnit"
    const val ACCESSALARM = "AccessAlarm"
    const val ACCESSALARMS = "AccessAlarms"
    const val ACCESSTIME = "AccessTime"
    const val ACCESSTIMEFILLED = "AccessTimeFilled"
    const val ACCESSIBILITY = "Accessibility"
    const val ACCESSIBILITYNEW = "AccessibilityNew"
    const val ACCESSIBLE = "Accessible"
    const val ACCESSIBLEFORWARD = "AccessibleForward"
    const val ACCOUNTBALANCE = "AccountBalance"
    const val ACCOUNTBALANCEWALLET = "AccountBalanceWallet"
    const val ACCOUNTBOX = "AccountBox"
    const val ACCOUNTCIRCLE = "AccountCircle"
    const val ACCOUNTTREE = "AccountTree"
    const val ADUNITS = "AdUnits"
    const val ADB = "Adb"
    const val ADD = "Add"
    const val ADDAPHOTO = "AddAPhoto"
    const val ADDALARM = "AddAlarm"
    const val ADDALERT = "AddAlert"
    const val ADDBOX = "AddBox"
    const val ADDBUSINESS = "AddBusiness"
    const val ADDCARD = "AddCard"
    const val ADDCHART = "AddChart"
    const val ADDCIRCLE = "AddCircle"
    const val ADDCIRCLEOUTLINE = "AddCircleOutline"
    const val ADDCOMMENT = "AddComment"
    const val ADDHOME = "AddHome"
    const val ADDHOMEWORK = "AddHomeWork"
    const val ADDICCALL = "AddIcCall"
    const val ADDLINK = "AddLink"
    const val ADDLOCATION = "AddLocation"
    const val ADDLOCATIONALT = "AddLocationAlt"
    const val ADDMODERATOR = "AddModerator"
    const val ADDPHOTOALTERNATE = "AddPhotoAlternate"
    const val ADDREACTION = "AddReaction"
    const val ADDROAD = "AddRoad"
    const val ADDSHOPPINGCART = "AddShoppingCart"
    const val ADDTASK = "AddTask"
    const val ADDTODRIVE = "AddToDrive"
    const val ADDTOHOMESCREEN = "AddToHomeScreen"
    const val ADDTOPHOTOS = "AddToPhotos"
    const val ADDTOQUEUE = "AddToQueue"
    const val ADFSCANNER = "AdfScanner"
    const val ADJUST = "Adjust"
    const val ADMINPANELSETTINGS = "AdminPanelSettings"
    const val ADSCLICK = "AdsClick"
    const val AGRICULTURE = "Agriculture"
    const val AIR = "Air"
    const val AIRLINESEATFLAT = "AirlineSeatFlat"
    const val AIRLINESEATFLATANGLED = "AirlineSeatFlatAngled"
    const val AIRLINESEATINDIVIDUALSUITE = "AirlineSeatIndividualSuite"
    const val AIRLINESEATLEGROOMEXTRA = "AirlineSeatLegroomExtra"
    const val AIRLINESEATLEGROOMNORMAL = "AirlineSeatLegroomNormal"
    const val AIRLINESEATLEGROOMREDUCED = "AirlineSeatLegroomReduced"
    const val AIRLINESEATRECLINEEXTRA = "AirlineSeatReclineExtra"
    const val AIRLINESEATRECLINENORMAL = "AirlineSeatReclineNormal"
    const val AIRLINESTOPS = "AirlineStops"
    const val AIRLINES = "Airlines"
    const val AIRPLANETICKET = "AirplaneTicket"
    const val AIRPLANEMODEACTIVE = "AirplanemodeActive"
    const val AIRPLANEMODEINACTIVE = "AirplanemodeInactive"
    const val AIRPLAY = "Airplay"
    const val AIRPORTSHUTTLE = "AirportShuttle"
    const val ALARM = "Alarm"
    const val ALARMADD = "AlarmAdd"
    const val ALARMOFF = "AlarmOff"
    const val ALARMON = "AlarmOn"
    const val ALBUM = "Album"
    const val ALIGNHORIZONTALCENTER = "AlignHorizontalCenter"
    const val ALIGNHORIZONTALLEFT = "AlignHorizontalLeft"
    const val ALIGNHORIZONTALRIGHT = "AlignHorizontalRight"
    const val ALIGNVERTICALBOTTOM = "AlignVerticalBottom"
    const val ALIGNVERTICALCENTER = "AlignVerticalCenter"
    const val ALIGNVERTICALTOP = "AlignVerticalTop"
    const val ALLINBOX = "AllInbox"
    const val ALLINCLUSIVE = "AllInclusive"
    const val ALLOUT = "AllOut"
    const val ALTROUTE = "AltRoute"
    const val ALTERNATEEMAIL = "AlternateEmail"
    const val AMPSTORIES = "AmpStories"
    const val ANALYTICS = "Analytics"
    const val ANCHOR = "Anchor"
    const val ANDROID = "Android"
    const val ANIMATION = "Animation"
    const val ANNOUNCEMENT = "Announcement"
    const val AOD = "Aod"
    const val APARTMENT = "Apartment"
    const val API = "Api"
    const val APPBLOCKING = "AppBlocking"
    const val APPREGISTRATION = "AppRegistration"
    const val APPSETTINGSALT = "AppSettingsAlt"
    const val APPSHORTCUT = "AppShortcut"
    const val APPROVAL = "Approval"
    const val APPS = "Apps"
    const val APPSOUTAGE = "AppsOutage"
    const val ARCHITECTURE = "Architecture"
    const val ARCHIVE = "Archive"
    const val AREACHART = "AreaChart"
    const val ARROWBACK = "ArrowBack"
    const val ARROWBACKIOS = "ArrowBackIos"
    const val ARROWBACKIOSNEW = "ArrowBackIosNew"
    const val ARROWCIRCLEDOWN = "ArrowCircleDown"
    const val ARROWCIRCLELEFT = "ArrowCircleLeft"
    const val ARROWCIRCLERIGHT = "ArrowCircleRight"
    const val ARROWCIRCLEUP = "ArrowCircleUp"
    const val ARROWDOWNWARD = "ArrowDownward"
    const val ARROWDROPDOWN = "ArrowDropDown"
    const val ARROWDROPDOWNCIRCLE = "ArrowDropDownCircle"
    const val ARROWDROPUP = "ArrowDropUp"
    const val ARROWFORWARD = "ArrowForward"
    const val ARROWFORWARDIOS = "ArrowForwardIos"
    const val ARROWLEFT = "ArrowLeft"
    const val ARROWOUTWARD = "ArrowOutward"
    const val ARROWRIGHT = "ArrowRight"
    const val ARROWRIGHTALT = "ArrowRightAlt"
    const val ARROWUPWARD = "ArrowUpward"
    const val ARTTRACK = "ArtTrack"
    const val ARTICLE = "Article"
    const val ASPECTRATIO = "AspectRatio"
    const val ASSESSMENT = "Assessment"
    const val ASSIGNMENT = "Assignment"
    const val ASSIGNMENTIND = "AssignmentInd"
    const val ASSIGNMENTLATE = "AssignmentLate"
    const val ASSIGNMENTRETURN = "AssignmentReturn"
    const val ASSIGNMENTRETURNED = "AssignmentReturned"
    const val ASSIGNMENTTURNEDIN = "AssignmentTurnedIn"
    const val ASSISTWALKER = "AssistWalker"
    const val ASSISTANT = "Assistant"
    const val ASSISTANTDIRECTION = "AssistantDirection"
    const val ASSISTANTPHOTO = "AssistantPhoto"
    const val ASSUREDWORKLOAD = "AssuredWorkload"
    const val ATM = "Atm"
    const val ATTACHEMAIL = "AttachEmail"
    const val ATTACHFILE = "AttachFile"
    const val ATTACHMONEY = "AttachMoney"
    const val ATTACHMENT = "Attachment"
    const val ATTRACTIONS = "Attractions"
    const val ATTRIBUTION = "Attribution"
    const val AUDIOFILE = "AudioFile"
    const val AUDIOTRACK = "Audiotrack"
    const val AUTOAWESOME = "AutoAwesome"
    const val AUTOAWESOMEMOSAIC = "AutoAwesomeMosaic"
    const val AUTOAWESOMEMOTION = "AutoAwesomeMotion"
    const val AUTODELETE = "AutoDelete"
    const val AUTOFIXHIGH = "AutoFixHigh"
    const val AUTOFIXNORMAL = "AutoFixNormal"
    const val AUTOFIXOFF = "AutoFixOff"
    const val AUTOGRAPH = "AutoGraph"
    const val AUTOMODE = "AutoMode"
    const val AUTOSTORIES = "AutoStories"
    const val AUTOFPSSELECT = "AutofpsSelect"
    const val AUTORENEW = "Autorenew"
    const val AVTIMER = "AvTimer"
    const val BABYCHANGINGSTATION = "BabyChangingStation"
    const val BACKHAND = "BackHand"
    const val BACKPACK = "Backpack"
    const val BACKSPACE = "Backspace"
    const val BACKUP = "Backup"
    const val BACKUPTABLE = "BackupTable"
    const val BADGE = "Badge"
    const val BAKERYDINING = "BakeryDining"
    const val BALANCE = "Balance"
    const val BALCONY = "Balcony"
    const val BALLOT = "Ballot"
    const val BARCHART = "BarChart"
    const val BATCHPREDICTION = "BatchPrediction"
    const val BATHROOM = "Bathroom"
    const val BATHTUB = "Bathtub"
    const val BATTERY0BAR = "Battery0Bar"
    const val BATTERY1BAR = "Battery1Bar"
    const val BATTERY2BAR = "Battery2Bar"
    const val BATTERY3BAR = "Battery3Bar"
    const val BATTERY4BAR = "Battery4Bar"
    const val BATTERY5BAR = "Battery5Bar"
    const val BATTERY6BAR = "Battery6Bar"
    const val BATTERYALERT = "BatteryAlert"
    const val BATTERYCHARGINGFULL = "BatteryChargingFull"
    const val BATTERYFULL = "BatteryFull"
    const val BATTERYSAVER = "BatterySaver"
    const val BATTERYSTD = "BatteryStd"
    const val BATTERYUNKNOWN = "BatteryUnknown"
    const val BEACHACCESS = "BeachAccess"
    const val BED = "Bed"
    const val BEDROOMBABY = "BedroomBaby"
    const val BEDROOMCHILD = "BedroomChild"
    const val BEDROOMPARENT = "BedroomParent"
    const val BEDTIME = "Bedtime"
    const val BEDTIMEOFF = "BedtimeOff"
    const val BEENHERE = "Beenhere"
    const val BENTO = "Bento"
    const val BIKESCOOTER = "BikeScooter"
    const val BIOTECH = "Biotech"
    const val BLENDER = "Blender"
    const val BLIND = "Blind"
    const val BLINDS = "Blinds"
    const val BLINDSCLOSED = "BlindsClosed"
    const val BLOCK = "Block"
    const val BLOODTYPE = "Bloodtype"
    const val BLUETOOTH = "Bluetooth"
    const val BLUETOOTHAUDIO = "BluetoothAudio"
    const val BLUETOOTHCONNECTED = "BluetoothConnected"
    const val BLUETOOTHDISABLED = "BluetoothDisabled"
    const val BLUETOOTHDRIVE = "BluetoothDrive"
    const val BLUETOOTHSEARCHING = "BluetoothSearching"
    const val BLURCIRCULAR = "BlurCircular"
    const val BLURLINEAR = "BlurLinear"
    const val BLUROFF = "BlurOff"
    const val BLURON = "BlurOn"
    const val BOLT = "Bolt"
    const val BOOK = "Book"
    const val BOOKONLINE = "BookOnline"
    const val BOOKMARK = "Bookmark"
    const val BOOKMARKADD = "BookmarkAdd"
    const val BOOKMARKADDED = "BookmarkAdded"
    const val BOOKMARKBORDER = "BookmarkBorder"
    const val BOOKMARKREMOVE = "BookmarkRemove"
    const val BOOKMARKS = "Bookmarks"
    const val BORDERALL = "BorderAll"
    const val BORDERBOTTOM = "BorderBottom"
    const val BORDERCLEAR = "BorderClear"
    const val BORDERCOLOR = "BorderColor"
    const val BORDERHORIZONTAL = "BorderHorizontal"
    const val BORDERINNER = "BorderInner"
    const val BORDERLEFT = "BorderLeft"
    const val BORDEROUTER = "BorderOuter"
    const val BORDERRIGHT = "BorderRight"
    const val BORDERSTYLE = "BorderStyle"
    const val BORDERTOP = "BorderTop"
    const val BORDERVERTICAL = "BorderVertical"
    const val BOY = "Boy"
    const val BRANDINGWATERMARK = "BrandingWatermark"
    const val BREAKFASTDINING = "BreakfastDining"
    const val BRIGHTNESS1 = "Brightness1"
    const val BRIGHTNESS2 = "Brightness2"
    const val BRIGHTNESS3 = "Brightness3"
    const val BRIGHTNESS4 = "Brightness4"
    const val BRIGHTNESS5 = "Brightness5"
    const val BRIGHTNESS6 = "Brightness6"
    const val BRIGHTNESS7 = "Brightness7"
    const val BRIGHTNESSAUTO = "BrightnessAuto"
    const val BRIGHTNESSHIGH = "BrightnessHigh"
    const val BRIGHTNESSLOW = "BrightnessLow"
    const val BRIGHTNESSMEDIUM = "BrightnessMedium"
    const val BROADCASTONHOME = "BroadcastOnHome"
    const val BROADCASTONPERSONAL = "BroadcastOnPersonal"
    const val BROKENIMAGE = "BrokenImage"
    const val BROWSEGALLERY = "BrowseGallery"
    const val BROWSERNOTSUPPORTED = "BrowserNotSupported"
    const val BROWSERUPDATED = "BrowserUpdated"
    const val BRUNCHDINING = "BrunchDining"
    const val BRUSH = "Brush"
    const val BUBBLECHART = "BubbleChart"
    const val BUGREPORT = "BugReport"
    const val BUILD = "Build"
    const val BUILDCIRCLE = "BuildCircle"
    const val BUNGALOW = "Bungalow"
    const val BURSTMODE = "BurstMode"
    const val BUSALERT = "BusAlert"
    const val BUSINESS = "Business"
    const val BUSINESSCENTER = "BusinessCenter"
    const val CABIN = "Cabin"
    const val CABLE = "Cable"
    const val CACHED = "Cached"
    const val CAKE = "Cake"
    const val CALCULATE = "Calculate"
    const val CALENDARMONTH = "CalendarMonth"
    const val CALENDARTODAY = "CalendarToday"
    const val CALENDARVIEWDAY = "CalendarViewDay"
    const val CALENDARVIEWMONTH = "CalendarViewMonth"
    const val CALENDARVIEWWEEK = "CalendarViewWeek"
    const val CALL = "Call"
    const val CALLEND = "CallEnd"
    const val CALLMADE = "CallMade"
    const val CALLMERGE = "CallMerge"
    const val CALLMISSED = "CallMissed"
    const val CALLMISSEDOUTGOING = "CallMissedOutgoing"
    const val CALLRECEIVED = "CallReceived"
    const val CALLSPLIT = "CallSplit"
    const val CALLTOACTION = "CallToAction"
    const val CAMERA = "Camera"
    const val CAMERAALT = "CameraAlt"
    const val CAMERAENHANCE = "CameraEnhance"
    const val CAMERAFRONT = "CameraFront"
    const val CAMERAINDOOR = "CameraIndoor"
    const val CAMERAOUTDOOR = "CameraOutdoor"
    const val CAMERAREAR = "CameraRear"
    const val CAMERAROLL = "CameraRoll"
    const val CAMERASWITCH = "Cameraswitch"
    const val CAMPAIGN = "Campaign"
    const val CANCEL = "Cancel"
    const val CANCELPRESENTATION = "CancelPresentation"
    const val CANCELSCHEDULESEND = "CancelScheduleSend"
    const val CANDLESTICKCHART = "CandlestickChart"
    const val CARCRASH = "CarCrash"
    const val CARRENTAL = "CarRental"
    const val CARREPAIR = "CarRepair"
    const val CARDGIFTCARD = "CardGiftcard"
    const val CARDMEMBERSHIP = "CardMembership"
    const val CARDTRAVEL = "CardTravel"
    const val CARPENTER = "Carpenter"
    const val CASES = "Cases"
    const val CASINO = "Casino"
    const val CAST = "Cast"
    const val CASTCONNECTED = "CastConnected"
    const val CASTFOREDUCATION = "CastForEducation"
    const val CASTLE = "Castle"
    const val CATCHINGPOKEMON = "CatchingPokemon"
    const val CATEGORY = "Category"
    const val CELEBRATION = "Celebration"
    const val CELLTOWER = "CellTower"
    const val CELLWIFI = "CellWifi"
    const val CENTERFOCUSSTRONG = "CenterFocusStrong"
    const val CENTERFOCUSWEAK = "CenterFocusWeak"
    const val CHAIR = "Chair"
    const val CHAIRALT = "ChairAlt"
    const val CHALET = "Chalet"
    const val CHANGECIRCLE = "ChangeCircle"
    const val CHANGEHISTORY = "ChangeHistory"
    const val CHARGINGSTATION = "ChargingStation"
    const val CHAT = "Chat"
    const val CHATBUBBLE = "ChatBubble"
    const val CHATBUBBLEOUTLINE = "ChatBubbleOutline"
    const val CHECK = "Check"
    const val CHECKBOX = "CheckBox"
    const val CHECKBOXOUTLINEBLANK = "CheckBoxOutlineBlank"
    const val CHECKCIRCLE = "CheckCircle"
    const val CHECKCIRCLEOUTLINE = "CheckCircleOutline"
    const val CHECKLIST = "Checklist"
    const val CHECKLISTRTL = "ChecklistRtl"
    const val CHECKROOM = "Checkroom"
    const val CHEVRONLEFT = "ChevronLeft"
    const val CHEVRONRIGHT = "ChevronRight"
    const val CHILDCARE = "ChildCare"
    const val CHILDFRIENDLY = "ChildFriendly"
    const val CHROMEREADERMODE = "ChromeReaderMode"
    const val CHURCH = "Church"
    const val CIRCLE = "Circle"
    const val CIRCLENOTIFICATIONS = "CircleNotifications"
    const val CLASS = "Class"
    const val CLEANHANDS = "CleanHands"
    const val CLEANINGSERVICES = "CleaningServices"
    const val CLEAR = "Clear"
    const val CLEARALL = "ClearAll"
    const val CLOSE = "Close"
    const val CLOSEFULLSCREEN = "CloseFullscreen"
    const val CLOSEDCAPTION = "ClosedCaption"
    const val CLOSEDCAPTIONDISABLED = "ClosedCaptionDisabled"
    const val CLOSEDCAPTIONOFF = "ClosedCaptionOff"
    const val CLOUD = "Cloud"
    const val CLOUDCIRCLE = "CloudCircle"
    const val CLOUDDONE = "CloudDone"
    const val CLOUDDOWNLOAD = "CloudDownload"
    const val CLOUDOFF = "CloudOff"
    const val CLOUDQUEUE = "CloudQueue"
    const val CLOUDSYNC = "CloudSync"
    const val CLOUDUPLOAD = "CloudUpload"
    const val CO2 = "Co2"
    const val COPRESENT = "CoPresent"
    const val CODE = "Code"
    const val CODEOFF = "CodeOff"
    const val COFFEE = "Coffee"
    const val COFFEEMAKER = "CoffeeMaker"
    const val COLLECTIONS = "Collections"
    const val COLLECTIONSBOOKMARK = "CollectionsBookmark"
    const val COLORLENS = "ColorLens"
    const val COLORIZE = "Colorize"
    const val COMMENT = "Comment"
    const val COMMENTBANK = "CommentBank"
    const val COMMENTSDISABLED = "CommentsDisabled"
    const val COMMIT = "Commit"
    const val COMMUTE = "Commute"
    const val COMPARE = "Compare"
    const val COMPAREARROWS = "CompareArrows"
    const val COMPASSCALIBRATION = "CompassCalibration"
    const val COMPOST = "Compost"
    const val COMPRESS = "Compress"
    const val COMPUTER = "Computer"
    const val CONFIRMATIONNUMBER = "ConfirmationNumber"
    const val CONNECTWITHOUTCONTACT = "ConnectWithoutContact"
    const val CONNECTEDTV = "ConnectedTv"
    const val CONNECTINGAIRPORTS = "ConnectingAirports"
    const val CONSTRUCTION = "Construction"
    const val CONTACTEMERGENCY = "ContactEmergency"
    const val CONTACTMAIL = "ContactMail"
    const val CONTACTPAGE = "ContactPage"
    const val CONTACTPHONE = "ContactPhone"
    const val CONTACTSUPPORT = "ContactSupport"
    const val CONTACTLESS = "Contactless"
    const val CONTACTS = "Contacts"
    const val CONTENTCOPY = "ContentCopy"
    const val CONTENTCUT = "ContentCut"
    const val CONTENTPASTE = "ContentPaste"
    const val CONTENTPASTEGO = "ContentPasteGo"
    const val CONTENTPASTEOFF = "ContentPasteOff"
    const val CONTENTPASTESEARCH = "ContentPasteSearch"
    const val CONTRAST = "Contrast"
    const val CONTROLCAMERA = "ControlCamera"
    const val CONTROLPOINT = "ControlPoint"
    const val CONTROLPOINTDUPLICATE = "ControlPointDuplicate"
    const val COOKIE = "Cookie"
    const val COPYALL = "CopyAll"
    const val COPYRIGHT = "Copyright"
    const val CORONAVIRUS = "Coronavirus"
    const val CORPORATEFARE = "CorporateFare"
    const val COTTAGE = "Cottage"
    const val COUNTERTOPS = "Countertops"
    const val CREATE = "Create"
    const val CREATENEWFOLDER = "CreateNewFolder"
    const val CREDITCARD = "CreditCard"
    const val CREDITCARDOFF = "CreditCardOff"
    const val CREDITSCORE = "CreditScore"
    const val CRIB = "Crib"
    const val CRISISALERT = "CrisisAlert"
    const val CROP = "Crop"
    const val CROP169 = "Crop169"
    const val CROP32 = "Crop32"
    const val CROP54 = "Crop54"
    const val CROP75 = "Crop75"
    const val CROPDIN = "CropDin"
    const val CROPFREE = "CropFree"
    const val CROPLANDSCAPE = "CropLandscape"
    const val CROPORIGINAL = "CropOriginal"
    const val CROPPORTRAIT = "CropPortrait"
    const val CROPROTATE = "CropRotate"
    const val CROPSQUARE = "CropSquare"
    const val CRUELTYFREE = "CrueltyFree"
    const val CSS = "Css"
    const val CURRENCYBITCOIN = "CurrencyBitcoin"
    const val CURRENCYEXCHANGE = "CurrencyExchange"
    const val CURRENCYFRANC = "CurrencyFranc"
    const val CURRENCYLIRA = "CurrencyLira"
    const val CURRENCYPOUND = "CurrencyPound"
    const val CURRENCYRUBLE = "CurrencyRuble"
    const val CURRENCYRUPEE = "CurrencyRupee"
    const val CURRENCYYEN = "CurrencyYen"
    const val CURRENCYYUAN = "CurrencyYuan"
    const val CURTAINS = "Curtains"
    const val CURTAINSCLOSED = "CurtainsClosed"
    const val CYCLONE = "Cyclone"
    const val DANGEROUS = "Dangerous"
    const val DARKMODE = "DarkMode"
    const val DASHBOARD = "Dashboard"
    const val DASHBOARDCUSTOMIZE = "DashboardCustomize"
    const val DATAARRAY = "DataArray"
    const val DATAEXPLORATION = "DataExploration"
    const val DATAOBJECT = "DataObject"
    const val DATASAVEROFF = "DataSaverOff"
    const val DATASAVERON = "DataSaverOn"
    const val DATATHRESHOLDING = "DataThresholding"
    const val DATAUSAGE = "DataUsage"
    const val DATASET = "Dataset"
    const val DATASETLINKED = "DatasetLinked"
    const val DATERANGE = "DateRange"
    const val DEBLUR = "Deblur"
    const val DECK = "Deck"
    const val DEHAZE = "Dehaze"
    const val DELETE = "Delete"
    const val DELETEFOREVER = "DeleteForever"
    const val DELETEOUTLINE = "DeleteOutline"
    const val DELETESWEEP = "DeleteSweep"
    const val DELIVERYDINING = "DeliveryDining"
    const val DENSITYLARGE = "DensityLarge"
    const val DENSITYMEDIUM = "DensityMedium"
    const val DENSITYSMALL = "DensitySmall"
    const val DEPARTUREBOARD = "DepartureBoard"
    const val DESCRIPTION = "Description"
    const val DESELECT = "Deselect"
    const val DESIGNSERVICES = "DesignServices"
    const val DESK = "Desk"
    const val DESKTOPACCESSDISABLED = "DesktopAccessDisabled"
    const val DESKTOPMAC = "DesktopMac"
    const val DESKTOPWINDOWS = "DesktopWindows"
    const val DETAILS = "Details"
    const val DEVELOPERBOARD = "DeveloperBoard"
    const val DEVELOPERBOARDOFF = "DeveloperBoardOff"
    const val DEVELOPERMODE = "DeveloperMode"
    const val DEVICEHUB = "DeviceHub"
    const val DEVICETHERMOSTAT = "DeviceThermostat"
    const val DEVICEUNKNOWN = "DeviceUnknown"
    const val DEVICES = "Devices"
    const val DEVICESFOLD = "DevicesFold"
    const val DEVICESOTHER = "DevicesOther"
    const val DIALERSIP = "DialerSip"
    const val DIALPAD = "Dialpad"
    const val DIAMOND = "Diamond"
    const val DIFFERENCE = "Difference"
    const val DINING = "Dining"
    const val DINNERDINING = "DinnerDining"
    const val DIRECTIONS = "Directions"
    const val DIRECTIONSBIKE = "DirectionsBike"
    const val DIRECTIONSBOAT = "DirectionsBoat"
    const val DIRECTIONSBOATFILLED = "DirectionsBoatFilled"
    const val DIRECTIONSBUS = "DirectionsBus"
    const val DIRECTIONSBUSFILLED = "DirectionsBusFilled"
    const val DIRECTIONSCAR = "DirectionsCar"
    const val DIRECTIONSCARFILLED = "DirectionsCarFilled"
    const val DIRECTIONSOFF = "DirectionsOff"
    const val DIRECTIONSRAILWAY = "DirectionsRailway"
    const val DIRECTIONSRAILWAYFILLED = "DirectionsRailwayFilled"
    const val DIRECTIONSRUN = "DirectionsRun"
    const val DIRECTIONSSUBWAY = "DirectionsSubway"
    const val DIRECTIONSSUBWAYFILLED = "DirectionsSubwayFilled"
    const val DIRECTIONSTRANSIT = "DirectionsTransit"
    const val DIRECTIONSTRANSITFILLED = "DirectionsTransitFilled"
    const val DIRECTIONSWALK = "DirectionsWalk"
    const val DIRTYLENS = "DirtyLens"
    const val DISABLEDBYDEFAULT = "DisabledByDefault"
    const val DISABLEDVISIBLE = "DisabledVisible"
    const val DISCFULL = "DiscFull"
    const val DISCOUNT = "Discount"
    const val DISPLAYSETTINGS = "DisplaySettings"
    const val DIVERSITY1 = "Diversity1"
    const val DIVERSITY2 = "Diversity2"
    const val DIVERSITY3 = "Diversity3"
    const val DNS = "Dns"
    const val DODISTURB = "DoDisturb"
    const val DODISTURBALT = "DoDisturbAlt"
    const val DODISTURBOFF = "DoDisturbOff"
    const val DODISTURBON = "DoDisturbOn"
    const val DONOTDISTURB = "DoNotDisturb"
    const val DONOTDISTURBALT = "DoNotDisturbAlt"
    const val DONOTDISTURBOFF = "DoNotDisturbOff"
    const val DONOTDISTURBON = "DoNotDisturbOn"
    const val DONOTDISTURBONTOTALSILENCE = "DoNotDisturbOnTotalSilence"
    const val DONOTSTEP = "DoNotStep"
    const val DONOTTOUCH = "DoNotTouch"
    const val DOCK = "Dock"
    const val DOCUMENTSCANNER = "DocumentScanner"
    const val DOMAIN = "Domain"
    const val DOMAINADD = "DomainAdd"
    const val DOMAINDISABLED = "DomainDisabled"
    const val DOMAINVERIFICATION = "DomainVerification"
    const val DONE = "Done"
    const val DONEALL = "DoneAll"
    const val DONEOUTLINE = "DoneOutline"
    const val DONUTLARGE = "DonutLarge"
    const val DONUTSMALL = "DonutSmall"
    const val DOORBACK = "DoorBack"
    const val DOORFRONT = "DoorFront"
    const val DOORSLIDING = "DoorSliding"
    const val DOORBELL = "Doorbell"
    const val DOUBLEARROW = "DoubleArrow"
    const val DOWNHILLSKIING = "DownhillSkiing"
    const val DOWNLOAD = "Download"
    const val DOWNLOADDONE = "DownloadDone"
    const val DOWNLOADFOROFFLINE = "DownloadForOffline"
    const val DOWNLOADING = "Downloading"
    const val DRAFTS = "Drafts"
    const val DRAGHANDLE = "DragHandle"
    const val DRAGINDICATOR = "DragIndicator"
    const val DRAW = "Draw"
    const val DRIVEETA = "DriveEta"
    const val DRIVEFILEMOVE = "DriveFileMove"
    const val DRIVEFILEMOVERTL = "DriveFileMoveRtl"
    const val DRIVEFILERENAMEOUTLINE = "DriveFileRenameOutline"
    const val DRIVEFOLDERUPLOAD = "DriveFolderUpload"
    const val DRY = "Dry"
    const val DRYCLEANING = "DryCleaning"
    const val DUO = "Duo"
    const val DVR = "Dvr"
    const val DYNAMICFEED = "DynamicFeed"
    const val DYNAMICFORM = "DynamicForm"
    const val EMOBILEDATA = "EMobiledata"
    const val EARBUDS = "Earbuds"
    const val EARBUDSBATTERY = "EarbudsBattery"
    const val EAST = "East"
    const val ECO = "Eco"
    const val EDGESENSORHIGH = "EdgesensorHigh"
    const val EDGESENSORLOW = "EdgesensorLow"
    const val EDIT = "Edit"
    const val EDITATTRIBUTES = "EditAttributes"
    const val EDITCALENDAR = "EditCalendar"
    const val EDITLOCATION = "EditLocation"
    const val EDITLOCATIONALT = "EditLocationAlt"
    const val EDITNOTE = "EditNote"
    const val EDITNOTIFICATIONS = "EditNotifications"
    const val EDITOFF = "EditOff"
    const val EDITROAD = "EditRoad"
    const val EGG = "Egg"
    const val EGGALT = "EggAlt"
    const val EJECT = "Eject"
    const val ELDERLY = "Elderly"
    const val ELDERLYWOMAN = "ElderlyWoman"
    const val ELECTRICBIKE = "ElectricBike"
    const val ELECTRICBOLT = "ElectricBolt"
    const val ELECTRICCAR = "ElectricCar"
    const val ELECTRICMETER = "ElectricMeter"
    const val ELECTRICMOPED = "ElectricMoped"
    const val ELECTRICRICKSHAW = "ElectricRickshaw"
    const val ELECTRICSCOOTER = "ElectricScooter"
    const val ELECTRICALSERVICES = "ElectricalServices"
    const val ELEVATOR = "Elevator"
    const val EMAIL = "Email"
    const val EMERGENCY = "Emergency"
    const val EMERGENCYRECORDING = "EmergencyRecording"
    const val EMERGENCYSHARE = "EmergencyShare"
    const val EMOJIEMOTIONS = "EmojiEmotions"
    const val EMOJIEVENTS = "EmojiEvents"
    const val EMOJIFLAGS = "EmojiFlags"
    const val EMOJIFOODBEVERAGE = "EmojiFoodBeverage"
    const val EMOJINATURE = "EmojiNature"
    const val EMOJIOBJECTS = "EmojiObjects"
    const val EMOJIPEOPLE = "EmojiPeople"
    const val EMOJISYMBOLS = "EmojiSymbols"
    const val EMOJITRANSPORTATION = "EmojiTransportation"
    const val ENERGYSAVINGSLEAF = "EnergySavingsLeaf"
    const val ENGINEERING = "Engineering"
    const val ENHANCEDENCRYPTION = "EnhancedEncryption"
    const val EQUALIZER = "Equalizer"
    const val ERROR = "Error"
    const val ERROROUTLINE = "ErrorOutline"
    const val ESCALATOR = "Escalator"
    const val ESCALATORWARNING = "EscalatorWarning"
    const val EURO = "Euro"
    const val EUROSYMBOL = "EuroSymbol"
    const val EVSTATION = "EvStation"
    const val EVENT = "Event"
    const val EVENTAVAILABLE = "EventAvailable"
    const val EVENTBUSY = "EventBusy"
    const val EVENTNOTE = "EventNote"
    const val EVENTREPEAT = "EventRepeat"
    const val EVENTSEAT = "EventSeat"
    const val EXITTOAPP = "ExitToApp"
    const val EXPAND = "Expand"
    const val EXPANDCIRCLEDOWN = "ExpandCircleDown"
    const val EXPANDLESS = "ExpandLess"
    const val EXPANDMORE = "ExpandMore"
    const val EXPLICIT = "Explicit"
    const val EXPLORE = "Explore"
    const val EXPLOREOFF = "ExploreOff"
    const val EXPOSURE = "Exposure"
    const val EXPOSURENEG1 = "ExposureNeg1"
    const val EXPOSURENEG2 = "ExposureNeg2"
    const val EXPOSUREPLUS1 = "ExposurePlus1"
    const val EXPOSUREPLUS2 = "ExposurePlus2"
    const val EXPOSUREZERO = "ExposureZero"
    const val EXTENSION = "Extension"
    const val EXTENSIONOFF = "ExtensionOff"
    const val FACE = "Face"
    const val FACE2 = "Face2"
    const val FACE3 = "Face3"
    const val FACE4 = "Face4"
    const val FACE5 = "Face5"
    const val FACE6 = "Face6"
    const val FACERETOUCHINGNATURAL = "FaceRetouchingNatural"
    const val FACERETOUCHINGOFF = "FaceRetouchingOff"
    const val FACEBOOK = "Facebook"
    const val FACTCHECK = "FactCheck"
    const val FACTORY = "Factory"
    const val FAMILYRESTROOM = "FamilyRestroom"
    const val FASTFORWARD = "FastForward"
    const val FASTREWIND = "FastRewind"
    const val FASTFOOD = "Fastfood"
    const val FAVORITE = "Favorite"
    const val FAVORITEBORDER = "FavoriteBorder"
    const val FAX = "Fax"
    const val FEATUREDPLAYLIST = "FeaturedPlayList"
    const val FEATUREDVIDEO = "FeaturedVideo"
    const val FEED = "Feed"
    const val FEEDBACK = "Feedback"
    const val FEMALE = "Female"
    const val FENCE = "Fence"
    const val FESTIVAL = "Festival"
    const val FIBERDVR = "FiberDvr"
    const val FIBERMANUALRECORD = "FiberManualRecord"
    const val FIBERNEW = "FiberNew"
    const val FIBERPIN = "FiberPin"
    const val FIBERSMARTRECORD = "FiberSmartRecord"
    const val FILECOPY = "FileCopy"
    const val FILEDOWNLOAD = "FileDownload"
    const val FILEDOWNLOADDONE = "FileDownloadDone"
    const val FILEDOWNLOADOFF = "FileDownloadOff"
    const val FILEOPEN = "FileOpen"
    const val FILEPRESENT = "FilePresent"
    const val FILEUPLOAD = "FileUpload"
    const val FILTER = "Filter"
    const val FILTER1 = "Filter1"
    const val FILTER2 = "Filter2"
    const val FILTER3 = "Filter3"
    const val FILTER4 = "Filter4"
    const val FILTER5 = "Filter5"
    const val FILTER6 = "Filter6"
    const val FILTER7 = "Filter7"
    const val FILTER8 = "Filter8"
    const val FILTER9 = "Filter9"
    const val FILTER9PLUS = "Filter9Plus"
    const val FILTERALT = "FilterAlt"
    const val FILTERALTOFF = "FilterAltOff"
    const val FILTERBANDW = "FilterBAndW"
    const val FILTERCENTERFOCUS = "FilterCenterFocus"
    const val FILTERDRAMA = "FilterDrama"
    const val FILTERFRAMES = "FilterFrames"
    const val FILTERHDR = "FilterHdr"
    const val FILTERLIST = "FilterList"
    const val FILTERLISTOFF = "FilterListOff"
    const val FILTERNONE = "FilterNone"
    const val FILTERTILTSHIFT = "FilterTiltShift"
    const val FILTERVINTAGE = "FilterVintage"
    const val FINDINPAGE = "FindInPage"
    const val FINDREPLACE = "FindReplace"
    const val FINGERPRINT = "Fingerprint"
    const val FIREEXTINGUISHER = "FireExtinguisher"
    const val FIREHYDRANTALT = "FireHydrantAlt"
    const val FIRETRUCK = "FireTruck"
    const val FIREPLACE = "Fireplace"
    const val FIRSTPAGE = "FirstPage"
    const val FITSCREEN = "FitScreen"
    const val FITBIT = "Fitbit"
    const val FITNESSCENTER = "FitnessCenter"
    const val FLAG = "Flag"
    const val FLAGCIRCLE = "FlagCircle"
    const val FLAKY = "Flaky"
    const val FLARE = "Flare"
    const val FLASHAUTO = "FlashAuto"
    const val FLASHOFF = "FlashOff"
    const val FLASHON = "FlashOn"
    const val FLASHLIGHTOFF = "FlashlightOff"
    const val FLASHLIGHTON = "FlashlightOn"
    const val FLATWARE = "Flatware"
    const val FLIGHT = "Flight"
    const val FLIGHTCLASS = "FlightClass"
    const val FLIGHTLAND = "FlightLand"
    const val FLIGHTTAKEOFF = "FlightTakeoff"
    const val FLIP = "Flip"
    const val FLIPCAMERAANDROID = "FlipCameraAndroid"
    const val FLIPCAMERAIOS = "FlipCameraIos"
    const val FLIPTOBACK = "FlipToBack"
    const val FLIPTOFRONT = "FlipToFront"
    const val FLOOD = "Flood"
    const val FLOURESCENT = "Flourescent"
    const val FLUORESCENT = "Fluorescent"
    const val FLUTTERDASH = "FlutterDash"
    const val FMDBAD = "FmdBad"
    const val FMDGOOD = "FmdGood"
    const val FOLDER = "Folder"
    const val FOLDERCOPY = "FolderCopy"
    const val FOLDERDELETE = "FolderDelete"
    const val FOLDEROFF = "FolderOff"
    const val FOLDEROPEN = "FolderOpen"
    const val FOLDERSHARED = "FolderShared"
    const val FOLDERSPECIAL = "FolderSpecial"
    const val FOLDERZIP = "FolderZip"
    const val FOLLOWTHESIGNS = "FollowTheSigns"
    const val FONTDOWNLOAD = "FontDownload"
    const val FONTDOWNLOADOFF = "FontDownloadOff"
    const val FOODBANK = "FoodBank"
    const val FOREST = "Forest"
    const val FORKLEFT = "ForkLeft"
    const val FORKRIGHT = "ForkRight"
    const val FORMATALIGNCENTER = "FormatAlignCenter"
    const val FORMATALIGNJUSTIFY = "FormatAlignJustify"
    const val FORMATALIGNLEFT = "FormatAlignLeft"
    const val FORMATALIGNRIGHT = "FormatAlignRight"
    const val FORMATBOLD = "FormatBold"
    const val FORMATCLEAR = "FormatClear"
    const val FORMATCOLORFILL = "FormatColorFill"
    const val FORMATCOLORRESET = "FormatColorReset"
    const val FORMATCOLORTEXT = "FormatColorText"
    const val FORMATINDENTDECREASE = "FormatIndentDecrease"
    const val FORMATINDENTINCREASE = "FormatIndentIncrease"
    const val FORMATITALIC = "FormatItalic"
    const val FORMATLINESPACING = "FormatLineSpacing"
    const val FORMATLISTBULLETED = "FormatListBulleted"
    const val FORMATLISTNUMBERED = "FormatListNumbered"
    const val FORMATLISTNUMBEREDRTL = "FormatListNumberedRtl"
    const val FORMATOVERLINE = "FormatOverline"
    const val FORMATPAINT = "FormatPaint"
    const val FORMATQUOTE = "FormatQuote"
    const val FORMATSHAPES = "FormatShapes"
    const val FORMATSIZE = "FormatSize"
    const val FORMATSTRIKETHROUGH = "FormatStrikethrough"
    const val FORMATTEXTDIRECTIONLTOR = "FormatTextdirectionLToR"
    const val FORMATTEXTDIRECTIONRTOL = "FormatTextdirectionRToL"
    const val FORMATUNDERLINED = "FormatUnderlined"
    const val FORT = "Fort"
    const val FORUM = "Forum"
    const val FORWARD = "Forward"
    const val FORWARD10 = "Forward10"
    const val FORWARD30 = "Forward30"
    const val FORWARD5 = "Forward5"
    const val FORWARDTOINBOX = "ForwardToInbox"
    const val FOUNDATION = "Foundation"
    const val FREEBREAKFAST = "FreeBreakfast"
    const val FREECANCELLATION = "FreeCancellation"
    const val FRONTHAND = "FrontHand"
    const val FULLSCREEN = "Fullscreen"
    const val FULLSCREENEXIT = "FullscreenExit"
    const val FUNCTIONS = "Functions"
    const val GMOBILEDATA = "GMobiledata"
    const val GTRANSLATE = "GTranslate"
    const val GAMEPAD = "Gamepad"
    const val GAMES = "Games"
    const val GARAGE = "Garage"
    const val GASMETER = "GasMeter"
    const val GAVEL = "Gavel"
    const val GENERATINGTOKENS = "GeneratingTokens"
    const val GESTURE = "Gesture"
    const val GETAPP = "GetApp"
    const val GIF = "Gif"
    const val GIFBOX = "GifBox"
    const val GIRL = "Girl"
    const val GITE = "Gite"
    const val GOLFCOURSE = "GolfCourse"
    const val GPPBAD = "GppBad"
    const val GPPGOOD = "GppGood"
    const val GPPMAYBE = "GppMaybe"
    const val GPSFIXED = "GpsFixed"
    const val GPSNOTFIXED = "GpsNotFixed"
    const val GPSOFF = "GpsOff"
    const val GRADE = "Grade"
    const val GRADIENT = "Gradient"
    const val GRADING = "Grading"
    const val GRAIN = "Grain"
    const val GRAPHICEQ = "GraphicEq"
    const val GRASS = "Grass"
    const val GRID3X3 = "Grid3x3"
    const val GRID4X4 = "Grid4x4"
    const val GRIDGOLDENRATIO = "GridGoldenratio"
    const val GRIDOFF = "GridOff"
    const val GRIDON = "GridOn"
    const val GRIDVIEW = "GridView"
    const val GROUP = "Group"
    const val GROUPADD = "GroupAdd"
    const val GROUPOFF = "GroupOff"
    const val GROUPREMOVE = "GroupRemove"
    const val GROUPWORK = "GroupWork"
    const val GROUPS = "Groups"
    const val GROUPS2 = "Groups2"
    const val GROUPS3 = "Groups3"
    const val HMOBILEDATA = "HMobiledata"
    const val HPLUSMOBILEDATA = "HPlusMobiledata"
    const val HAIL = "Hail"
    const val HANDSHAKE = "Handshake"
    const val HANDYMAN = "Handyman"
    const val HARDWARE = "Hardware"
    const val HD = "Hd"
    const val HDRAUTO = "HdrAuto"
    const val HDRAUTOSELECT = "HdrAutoSelect"
    const val HDRENHANCEDSELECT = "HdrEnhancedSelect"
    const val HDROFF = "HdrOff"
    const val HDROFFSELECT = "HdrOffSelect"
    const val HDRON = "HdrOn"
    const val HDRONSELECT = "HdrOnSelect"
    const val HDRPLUS = "HdrPlus"
    const val HDRSTRONG = "HdrStrong"
    const val HDRWEAK = "HdrWeak"
    const val HEADPHONES = "Headphones"
    const val HEADPHONESBATTERY = "HeadphonesBattery"
    const val HEADSET = "Headset"
    const val HEADSETMIC = "HeadsetMic"
    const val HEADSETOFF = "HeadsetOff"
    const val HEALING = "Healing"
    const val HEALTHANDSAFETY = "HealthAndSafety"
    const val HEARING = "Hearing"
    const val HEARINGDISABLED = "HearingDisabled"
    const val HEARTBROKEN = "HeartBroken"
    const val HEATPUMP = "HeatPump"
    const val HEIGHT = "Height"
    const val HELP = "Help"
    const val HELPCENTER = "HelpCenter"
    const val HELPOUTLINE = "HelpOutline"
    const val HEVC = "Hevc"
    const val HEXAGON = "Hexagon"
    const val HIDEIMAGE = "HideImage"
    const val HIDESOURCE = "HideSource"
    const val HIGHQUALITY = "HighQuality"
    const val HIGHLIGHT = "Highlight"
    const val HIGHLIGHTALT = "HighlightAlt"
    const val HIGHLIGHTOFF = "HighlightOff"
    const val HIKING = "Hiking"
    const val HISTORY = "History"
    const val HISTORYEDU = "HistoryEdu"
    const val HISTORYTOGGLEOFF = "HistoryToggleOff"
    const val HIVE = "Hive"
    const val HLS = "Hls"
    const val HLSOFF = "HlsOff"
    const val HOLIDAYVILLAGE = "HolidayVillage"
    const val HOME = "Home"
    const val HOMEMAX = "HomeMax"
    const val HOMEMINI = "HomeMini"
    const val HOMEREPAIRSERVICE = "HomeRepairService"
    const val HOMEWORK = "HomeWork"
    const val HORIZONTALDISTRIBUTE = "HorizontalDistribute"
    const val HORIZONTALRULE = "HorizontalRule"
    const val HORIZONTALSPLIT = "HorizontalSplit"
    const val HOTTUB = "HotTub"
    const val HOTEL = "Hotel"
    const val HOTELCLASS = "HotelClass"
    const val HOURGLASSBOTTOM = "HourglassBottom"
    const val HOURGLASSDISABLED = "HourglassDisabled"
    const val HOURGLASSEMPTY = "HourglassEmpty"
    const val HOURGLASSFULL = "HourglassFull"
    const val HOURGLASSTOP = "HourglassTop"
    const val HOUSE = "House"
    const val HOUSESIDING = "HouseSiding"
    const val HOUSEBOAT = "Houseboat"
    const val HOWTOREG = "HowToReg"
    const val HOWTOVOTE = "HowToVote"
    const val HTML = "Html"
    const val HTTP = "Http"
    const val HTTPS = "Https"
    const val HUB = "Hub"
    const val HVAC = "Hvac"
    const val ICESKATING = "IceSkating"
    const val ICECREAM = "Icecream"
    const val IMAGE = "Image"
    const val IMAGEASPECTRATIO = "ImageAspectRatio"
    const val IMAGENOTSUPPORTED = "ImageNotSupported"
    const val IMAGESEARCH = "ImageSearch"
    const val IMAGESEARCHROLLER = "ImagesearchRoller"
    const val IMPORTCONTACTS = "ImportContacts"
    const val IMPORTEXPORT = "ImportExport"
    const val IMPORTANTDEVICES = "ImportantDevices"
    const val INBOX = "Inbox"
    const val INCOMPLETECIRCLE = "IncompleteCircle"
    const val INDETERMINATECHECKBOX = "IndeterminateCheckBox"
    const val INFO = "Info"
    const val INPUT = "Input"
    const val INSERTCHART = "InsertChart"
    const val INSERTCHARTOUTLINED = "InsertChartOutlined"
    const val INSERTCOMMENT = "InsertComment"
    const val INSERTDRIVEFILE = "InsertDriveFile"
    const val INSERTEMOTICON = "InsertEmoticon"
    const val INSERTINVITATION = "InsertInvitation"
    const val INSERTLINK = "InsertLink"
    const val INSERTPAGEBREAK = "InsertPageBreak"
    const val INSERTPHOTO = "InsertPhoto"
    const val INSIGHTS = "Insights"
    const val INSTALLDESKTOP = "InstallDesktop"
    const val INSTALLMOBILE = "InstallMobile"
    const val INTEGRATIONINSTRUCTIONS = "IntegrationInstructions"
    const val INTERESTS = "Interests"
    const val INTERPRETERMODE = "InterpreterMode"
    const val INVENTORY = "Inventory"
    const val INVENTORY2 = "Inventory2"
    const val INVERTCOLORS = "InvertColors"
    const val INVERTCOLORSOFF = "InvertColorsOff"
    const val IOSSHARE = "IosShare"
    const val IRON = "Iron"
    const val ISO = "Iso"
    const val JAVASCRIPT = "Javascript"
    const val JOINFULL = "JoinFull"
    const val JOININNER = "JoinInner"
    const val JOINLEFT = "JoinLeft"
    const val JOINRIGHT = "JoinRight"
    const val KAYAKING = "Kayaking"
    const val KEBABDINING = "KebabDining"
    const val KEY = "Key"
    const val KEYOFF = "KeyOff"
    const val KEYBOARD = "Keyboard"
    const val KEYBOARDALT = "KeyboardAlt"
    const val KEYBOARDARROWDOWN = "KeyboardArrowDown"
    const val KEYBOARDARROWLEFT = "KeyboardArrowLeft"
    const val KEYBOARDARROWRIGHT = "KeyboardArrowRight"
    const val KEYBOARDARROWUP = "KeyboardArrowUp"
    const val KEYBOARDBACKSPACE = "KeyboardBackspace"
    const val KEYBOARDCAPSLOCK = "KeyboardCapslock"
    const val KEYBOARDCOMMANDKEY = "KeyboardCommandKey"
    const val KEYBOARDCONTROLKEY = "KeyboardControlKey"
    const val KEYBOARDDOUBLEARROWDOWN = "KeyboardDoubleArrowDown"
    const val KEYBOARDDOUBLEARROWLEFT = "KeyboardDoubleArrowLeft"
    const val KEYBOARDDOUBLEARROWRIGHT = "KeyboardDoubleArrowRight"
    const val KEYBOARDDOUBLEARROWUP = "KeyboardDoubleArrowUp"
    const val KEYBOARDHIDE = "KeyboardHide"
    const val KEYBOARDOPTIONKEY = "KeyboardOptionKey"
    const val KEYBOARDRETURN = "KeyboardReturn"
    const val KEYBOARDTAB = "KeyboardTab"
    const val KEYBOARDVOICE = "KeyboardVoice"
    const val KINGBED = "KingBed"
    const val KITCHEN = "Kitchen"
    const val KITESURFING = "Kitesurfing"
    const val LABEL = "Label"
    const val LABELIMPORTANT = "LabelImportant"
    const val LABELOFF = "LabelOff"
    const val LAN = "Lan"
    const val LANDSCAPE = "Landscape"
    const val LANDSLIDE = "Landslide"
    const val LANGUAGE = "Language"
    const val LAPTOP = "Laptop"
    const val LAPTOPCHROMEBOOK = "LaptopChromebook"
    const val LAPTOPMAC = "LaptopMac"
    const val LAPTOPWINDOWS = "LaptopWindows"
    const val LASTPAGE = "LastPage"
    const val LAUNCH = "Launch"
    const val LAYERS = "Layers"
    const val LAYERSCLEAR = "LayersClear"
    const val LEADERBOARD = "Leaderboard"
    const val LEAKADD = "LeakAdd"
    const val LEAKREMOVE = "LeakRemove"
    const val LEAVEBAGSATHOME = "LeaveBagsAtHome"
    const val LEGENDTOGGLE = "LegendToggle"
    const val LENS = "Lens"
    const val LENSBLUR = "LensBlur"
    const val LIBRARYADD = "LibraryAdd"
    const val LIBRARYADDCHECK = "LibraryAddCheck"
    const val LIBRARYBOOKS = "LibraryBooks"
    const val LIBRARYMUSIC = "LibraryMusic"
    const val LIGHT = "Light"
    const val LIGHTMODE = "LightMode"
    const val LIGHTBULB = "Lightbulb"
    const val LIGHTBULBCIRCLE = "LightbulbCircle"
    const val LINEAXIS = "LineAxis"
    const val LINESTYLE = "LineStyle"
    const val LINEWEIGHT = "LineWeight"
    const val LINEARSCALE = "LinearScale"
    const val LINK = "Link"
    const val LINKOFF = "LinkOff"
    const val LINKEDCAMERA = "LinkedCamera"
    const val LIQUOR = "Liquor"
    const val LIST = "List"
    const val LISTALT = "ListAlt"
    const val LIVEHELP = "LiveHelp"
    const val LIVETV = "LiveTv"
    const val LIVING = "Living"
    const val LOCALACTIVITY = "LocalActivity"
    const val LOCALAIRPORT = "LocalAirport"
    const val LOCALATM = "LocalAtm"
    const val LOCALBAR = "LocalBar"
    const val LOCALCAFE = "LocalCafe"
    const val LOCALCARWASH = "LocalCarWash"
    const val LOCALCONVENIENCESTORE = "LocalConvenienceStore"
    const val LOCALDINING = "LocalDining"
    const val LOCALDRINK = "LocalDrink"
    const val LOCALFIREDEPARTMENT = "LocalFireDepartment"
    const val LOCALFLORIST = "LocalFlorist"
    const val LOCALGASSTATION = "LocalGasStation"
    const val LOCALGROCERYSTORE = "LocalGroceryStore"
    const val LOCALHOSPITAL = "LocalHospital"
    const val LOCALHOTEL = "LocalHotel"
    const val LOCALLAUNDRYSERVICE = "LocalLaundryService"
    const val LOCALLIBRARY = "LocalLibrary"
    const val LOCALMALL = "LocalMall"
    const val LOCALMOVIES = "LocalMovies"
    const val LOCALOFFER = "LocalOffer"
    const val LOCALPARKING = "LocalParking"
    const val LOCALPHARMACY = "LocalPharmacy"
    const val LOCALPHONE = "LocalPhone"
    const val LOCALPIZZA = "LocalPizza"
    const val LOCALPLAY = "LocalPlay"
    const val LOCALPOLICE = "LocalPolice"
    const val LOCALPOSTOFFICE = "LocalPostOffice"
    const val LOCALPRINTSHOP = "LocalPrintshop"
    const val LOCALSEE = "LocalSee"
    const val LOCALSHIPPING = "LocalShipping"
    const val LOCALTAXI = "LocalTaxi"
    const val LOCATIONCITY = "LocationCity"
    const val LOCATIONDISABLED = "LocationDisabled"
    const val LOCATIONOFF = "LocationOff"
    const val LOCATIONON = "LocationOn"
    const val LOCATIONSEARCHING = "LocationSearching"
    const val LOCK = "Lock"
    const val LOCKCLOCK = "LockClock"
    const val LOCKOPEN = "LockOpen"
    const val LOCKPERSON = "LockPerson"
    const val LOCKRESET = "LockReset"
    const val LOGIN = "Login"
    const val LOGODEV = "LogoDev"
    const val LOGOUT = "Logout"
    const val LOOKS = "Looks"
    const val LOOKS3 = "Looks3"
    const val LOOKS4 = "Looks4"
    const val LOOKS5 = "Looks5"
    const val LOOKS6 = "Looks6"
    const val LOOKSONE = "LooksOne"
    const val LOOKSTWO = "LooksTwo"
    const val LOOP = "Loop"
    const val LOUPE = "Loupe"
    const val LOWPRIORITY = "LowPriority"
    const val LOYALTY = "Loyalty"
    const val LTEMOBILEDATA = "LteMobiledata"
    const val LTEPLUSMOBILEDATA = "LtePlusMobiledata"
    const val LUGGAGE = "Luggage"
    const val LUNCHDINING = "LunchDining"
    const val LYRICS = "Lyrics"
    const val MACROOFF = "MacroOff"
    const val MAIL = "Mail"
    const val MAILLOCK = "MailLock"
    const val MAILOUTLINE = "MailOutline"
    const val MALE = "Male"
    const val MAN = "Man"
    const val MAN2 = "Man2"
    const val MAN3 = "Man3"
    const val MAN4 = "Man4"
    const val MANAGEACCOUNTS = "ManageAccounts"
    const val MANAGEHISTORY = "ManageHistory"
    const val MANAGESEARCH = "ManageSearch"
    const val MAP = "Map"
    const val MAPSHOMEWORK = "MapsHomeWork"
    const val MAPSUGC = "MapsUgc"
    const val MARGIN = "Margin"
    const val MARKASUNREAD = "MarkAsUnread"
    const val MARKCHATREAD = "MarkChatRead"
    const val MARKCHATUNREAD = "MarkChatUnread"
    const val MARKEMAILREAD = "MarkEmailRead"
    const val MARKEMAILUNREAD = "MarkEmailUnread"
    const val MARKUNREADCHATALT = "MarkUnreadChatAlt"
    const val MARKUNREAD = "Markunread"
    const val MARKUNREADMAILBOX = "MarkunreadMailbox"
    const val MASKS = "Masks"
    const val MAXIMIZE = "Maximize"
    const val MEDIABLUETOOTHOFF = "MediaBluetoothOff"
    const val MEDIABLUETOOTHON = "MediaBluetoothOn"
    const val MEDIATION = "Mediation"
    const val MEDICALINFORMATION = "MedicalInformation"
    const val MEDICALSERVICES = "MedicalServices"
    const val MEDICATION = "Medication"
    const val MEETINGROOM = "MeetingRoom"
    const val MEMORY = "Memory"
    const val MENU = "Menu"
    const val MENUBOOK = "MenuBook"
    const val MENUOPEN = "MenuOpen"
    const val MERGE = "Merge"
    const val MERGETYPE = "MergeType"
    const val MESSAGE = "Message"
    const val MIC = "Mic"
    const val MICEXTERNALOFF = "MicExternalOff"
    const val MICEXTERNALON = "MicExternalOn"
    const val MICNONE = "MicNone"
    const val MICOFF = "MicOff"
    const val MICROWAVE = "Microwave"
    const val MILITARYTECH = "MilitaryTech"
    const val MINIMIZE = "Minimize"
    const val MINORCRASH = "MinorCrash"
    const val MISCELLANEOUSSERVICES = "MiscellaneousServices"
    const val MISSEDVIDEOCALL = "MissedVideoCall"
    const val MMS = "Mms"
    const val MOBILEFRIENDLY = "MobileFriendly"
    const val MOBILEOFF = "MobileOff"
    const val MOBILESCREENSHARE = "MobileScreenShare"
    const val MOBILEDATAOFF = "MobiledataOff"
    const val MODE = "Mode"
    const val MODECOMMENT = "ModeComment"
    const val MODEEDIT = "ModeEdit"
    const val MODEEDITOUTLINE = "ModeEditOutline"
    const val MODEFANOFF = "ModeFanOff"
    const val MODENIGHT = "ModeNight"
    const val MODEOFTRAVEL = "ModeOfTravel"
    const val MODESTANDBY = "ModeStandby"
    const val MODELTRAINING = "ModelTraining"
    const val MONETIZATIONON = "MonetizationOn"
    const val MONEY = "Money"
    const val MONEYOFF = "MoneyOff"
    const val MONEYOFFCSRED = "MoneyOffCsred"
    const val MONITOR = "Monitor"
    const val MONITORHEART = "MonitorHeart"
    const val MONITORWEIGHT = "MonitorWeight"
    const val MONOCHROMEPHOTOS = "MonochromePhotos"
    const val MOOD = "Mood"
    const val MOODBAD = "MoodBad"
    const val MOPED = "Moped"
    const val MORE = "More"
    const val MOREHORIZ = "MoreHoriz"
    const val MORETIME = "MoreTime"
    const val MOREVERT = "MoreVert"
    const val MOSQUE = "Mosque"
    const val MOTIONPHOTOSAUTO = "MotionPhotosAuto"
    const val MOTIONPHOTOSOFF = "MotionPhotosOff"
    const val MOTIONPHOTOSON = "MotionPhotosOn"
    const val MOTIONPHOTOSPAUSE = "MotionPhotosPause"
    const val MOTIONPHOTOSPAUSED = "MotionPhotosPaused"
    const val MOTORCYCLE = "Motorcycle"
    const val MOUSE = "Mouse"
    const val MOVEDOWN = "MoveDown"
    const val MOVETOINBOX = "MoveToInbox"
    const val MOVEUP = "MoveUp"
    const val MOVIE = "Movie"
    const val MOVIECREATION = "MovieCreation"
    const val MOVIEFILTER = "MovieFilter"
    const val MOVING = "Moving"
    const val MP = "Mp"
    const val MULTILINECHART = "MultilineChart"
    const val MULTIPLESTOP = "MultipleStop"
    const val MUSEUM = "Museum"
    const val MUSICNOTE = "MusicNote"
    const val MUSICOFF = "MusicOff"
    const val MUSICVIDEO = "MusicVideo"
    const val MYLOCATION = "MyLocation"
    const val NAT = "Nat"
    const val NATURE = "Nature"
    const val NATUREPEOPLE = "NaturePeople"
    const val NAVIGATEBEFORE = "NavigateBefore"
    const val NAVIGATENEXT = "NavigateNext"
    const val NAVIGATION = "Navigation"
    const val NEARME = "NearMe"
    const val NEARMEDISABLED = "NearMeDisabled"
    const val NEARBYERROR = "NearbyError"
    const val NEARBYOFF = "NearbyOff"
    const val NESTCAMWIREDSTAND = "NestCamWiredStand"
    const val NETWORKCELL = "NetworkCell"
    const val NETWORKCHECK = "NetworkCheck"
    const val NETWORKLOCKED = "NetworkLocked"
    const val NETWORKPING = "NetworkPing"
    const val NETWORKWIFI = "NetworkWifi"
    const val NETWORKWIFI1BAR = "NetworkWifi1Bar"
    const val NETWORKWIFI2BAR = "NetworkWifi2Bar"
    const val NETWORKWIFI3BAR = "NetworkWifi3Bar"
    const val NEWLABEL = "NewLabel"
    const val NEWRELEASES = "NewReleases"
    const val NEWSPAPER = "Newspaper"
    const val NEXTPLAN = "NextPlan"
    const val NEXTWEEK = "NextWeek"
    const val NFC = "Nfc"
    const val NIGHTSHELTER = "NightShelter"
    const val NIGHTLIFE = "Nightlife"
    const val NIGHTLIGHT = "Nightlight"
    const val NIGHTLIGHTROUND = "NightlightRound"
    const val NIGHTSSTAY = "NightsStay"
    const val NOACCOUNTS = "NoAccounts"
    const val NOADULTCONTENT = "NoAdultContent"
    const val NOBACKPACK = "NoBackpack"
    const val NOCELL = "NoCell"
    const val NOCRASH = "NoCrash"
    const val NODRINKS = "NoDrinks"
    const val NOENCRYPTION = "NoEncryption"
    const val NOENCRYPTIONGMAILERRORRED = "NoEncryptionGmailerrorred"
    const val NOFLASH = "NoFlash"
    const val NOFOOD = "NoFood"
    const val NOLUGGAGE = "NoLuggage"
    const val NOMEALS = "NoMeals"
    const val NOMEETINGROOM = "NoMeetingRoom"
    const val NOPHOTOGRAPHY = "NoPhotography"
    const val NOSIM = "NoSim"
    const val NOSTROLLER = "NoStroller"
    const val NOTRANSFER = "NoTransfer"
    const val NOISEAWARE = "NoiseAware"
    const val NOISECONTROLOFF = "NoiseControlOff"
    const val NORDICWALKING = "NordicWalking"
    const val NORTH = "North"
    const val NORTHEAST = "NorthEast"
    const val NORTHWEST = "NorthWest"
    const val NOTACCESSIBLE = "NotAccessible"
    const val NOTINTERESTED = "NotInterested"
    const val NOTLISTEDLOCATION = "NotListedLocation"
    const val NOTSTARTED = "NotStarted"
    const val NOTE = "Note"
    const val NOTEADD = "NoteAdd"
    const val NOTEALT = "NoteAlt"
    const val NOTES = "Notes"
    const val NOTIFICATIONADD = "NotificationAdd"
    const val NOTIFICATIONIMPORTANT = "NotificationImportant"
    const val NOTIFICATIONS = "Notifications"
    const val NOTIFICATIONSACTIVE = "NotificationsActive"
    const val NOTIFICATIONSNONE = "NotificationsNone"
    const val NOTIFICATIONSOFF = "NotificationsOff"
    const val NOTIFICATIONSPAUSED = "NotificationsPaused"
    const val NUMBERS = "Numbers"
    const val OFFLINEBOLT = "OfflineBolt"
    const val OFFLINEPIN = "OfflinePin"
    const val OFFLINESHARE = "OfflineShare"
    const val OILBARREL = "OilBarrel"
    const val ONDEVICETRAINING = "OnDeviceTraining"
    const val ONDEMANDVIDEO = "OndemandVideo"
    const val ONLINEPREDICTION = "OnlinePrediction"
    const val OPACITY = "Opacity"
    const val OPENINBROWSER = "OpenInBrowser"
    const val OPENINFULL = "OpenInFull"
    const val OPENINNEW = "OpenInNew"
    const val OPENINNEWOFF = "OpenInNewOff"
    const val OPENWITH = "OpenWith"
    const val OTHERHOUSES = "OtherHouses"
    const val OUTBOND = "Outbond"
    const val OUTBOUND = "Outbound"
    const val OUTBOX = "Outbox"
    const val OUTDOORGRILL = "OutdoorGrill"
    const val OUTLET = "Outlet"
    const val OUTLINEDFLAG = "OutlinedFlag"
    const val OUTPUT = "Output"
    const val PADDING = "Padding"
    const val PAGES = "Pages"
    const val PAGEVIEW = "Pageview"
    const val PAID = "Paid"
    const val PALETTE = "Palette"
    const val PANTOOL = "PanTool"
    const val PANTOOLALT = "PanToolAlt"
    const val PANORAMA = "Panorama"
    const val PANORAMAFISHEYE = "PanoramaFishEye"
    const val PANORAMAHORIZONTAL = "PanoramaHorizontal"
    const val PANORAMAHORIZONTALSELECT = "PanoramaHorizontalSelect"
    const val PANORAMAPHOTOSPHERE = "PanoramaPhotosphere"
    const val PANORAMAPHOTOSPHERESELECT = "PanoramaPhotosphereSelect"
    const val PANORAMAVERTICAL = "PanoramaVertical"
    const val PANORAMAVERTICALSELECT = "PanoramaVerticalSelect"
    const val PANORAMAWIDEANGLE = "PanoramaWideAngle"
    const val PANORAMAWIDEANGLESELECT = "PanoramaWideAngleSelect"
    const val PARAGLIDING = "Paragliding"
    const val PARK = "Park"
    const val PARTYMODE = "PartyMode"
    const val PASSWORD = "Password"
    const val PATTERN = "Pattern"
    const val PAUSE = "Pause"
    const val PAUSECIRCLE = "PauseCircle"
    const val PAUSECIRCLEFILLED = "PauseCircleFilled"
    const val PAUSECIRCLEOUTLINE = "PauseCircleOutline"
    const val PAUSEPRESENTATION = "PausePresentation"
    const val PAYMENT = "Payment"
    const val PAYMENTS = "Payments"
    const val PEDALBIKE = "PedalBike"
    const val PENDING = "Pending"
    const val PENDINGACTIONS = "PendingActions"
    const val PENTAGON = "Pentagon"
    const val PEOPLE = "People"
    const val PEOPLEALT = "PeopleAlt"
    const val PEOPLEOUTLINE = "PeopleOutline"
    const val PERCENT = "Percent"
    const val PERMCAMERAMIC = "PermCameraMic"
    const val PERMCONTACTCALENDAR = "PermContactCalendar"
    const val PERMDATASETTING = "PermDataSetting"
    const val PERMDEVICEINFORMATION = "PermDeviceInformation"
    const val PERMIDENTITY = "PermIdentity"
    const val PERMMEDIA = "PermMedia"
    const val PERMPHONEMSG = "PermPhoneMsg"
    const val PERMSCANWIFI = "PermScanWifi"
    const val PERSON = "Person"
    const val PERSON2 = "Person2"
    const val PERSON3 = "Person3"
    const val PERSON4 = "Person4"
    const val PERSONADD = "PersonAdd"
    const val PERSONADDALT = "PersonAddAlt"
    const val PERSONADDALT1 = "PersonAddAlt1"
    const val PERSONADDDISABLED = "PersonAddDisabled"
    const val PERSONOFF = "PersonOff"
    const val PERSONOUTLINE = "PersonOutline"
    const val PERSONPIN = "PersonPin"
    const val PERSONPINCIRCLE = "PersonPinCircle"
    const val PERSONREMOVE = "PersonRemove"
    const val PERSONREMOVEALT1 = "PersonRemoveAlt1"
    const val PERSONSEARCH = "PersonSearch"
    const val PERSONALINJURY = "PersonalInjury"
    const val PERSONALVIDEO = "PersonalVideo"
    const val PESTCONTROL = "PestControl"
    const val PESTCONTROLRODENT = "PestControlRodent"
    const val PETS = "Pets"
    const val PHISHING = "Phishing"
    const val PHONE = "Phone"
    const val PHONEANDROID = "PhoneAndroid"
    const val PHONEBLUETOOTHSPEAKER = "PhoneBluetoothSpeaker"
    const val PHONECALLBACK = "PhoneCallback"
    const val PHONEDISABLED = "PhoneDisabled"
    const val PHONEENABLED = "PhoneEnabled"
    const val PHONEFORWARDED = "PhoneForwarded"
    const val PHONEINTALK = "PhoneInTalk"
    const val PHONEIPHONE = "PhoneIphone"
    const val PHONELOCKED = "PhoneLocked"
    const val PHONEMISSED = "PhoneMissed"
    const val PHONEPAUSED = "PhonePaused"
    const val PHONELINK = "Phonelink"
    const val PHONELINKERASE = "PhonelinkErase"
    const val PHONELINKLOCK = "PhonelinkLock"
    const val PHONELINKOFF = "PhonelinkOff"
    const val PHONELINKRING = "PhonelinkRing"
    const val PHONELINKSETUP = "PhonelinkSetup"
    const val PHOTO = "Photo"
    const val PHOTOALBUM = "PhotoAlbum"
    const val PHOTOCAMERA = "PhotoCamera"
    const val PHOTOCAMERABACK = "PhotoCameraBack"
    const val PHOTOCAMERAFRONT = "PhotoCameraFront"
    const val PHOTOFILTER = "PhotoFilter"
    const val PHOTOLIBRARY = "PhotoLibrary"
    const val PHOTOSIZESELECTACTUAL = "PhotoSizeSelectActual"
    const val PHOTOSIZESELECTLARGE = "PhotoSizeSelectLarge"
    const val PHOTOSIZESELECTSMALL = "PhotoSizeSelectSmall"
    const val PHP = "Php"
    const val PIANO = "Piano"
    const val PIANOOFF = "PianoOff"
    const val PICTUREASPDF = "PictureAsPdf"
    const val PICTUREINPICTURE = "PictureInPicture"
    const val PICTUREINPICTUREALT = "PictureInPictureAlt"
    const val PIECHART = "PieChart"
    const val PIECHARTOUTLINE = "PieChartOutline"
    const val PIN = "Pin"
    const val PINDROP = "PinDrop"
    const val PINEND = "PinEnd"
    const val PININVOKE = "PinInvoke"
    const val PINCH = "Pinch"
    const val PIVOTTABLECHART = "PivotTableChart"
    const val PIX = "Pix"
    const val PLACE = "Place"
    const val PLAGIARISM = "Plagiarism"
    const val PLAYARROW = "PlayArrow"
    const val PLAYCIRCLE = "PlayCircle"
    const val PLAYCIRCLEFILLED = "PlayCircleFilled"
    const val PLAYCIRCLEOUTLINE = "PlayCircleOutline"
    const val PLAYDISABLED = "PlayDisabled"
    const val PLAYFORWORK = "PlayForWork"
    const val PLAYLESSON = "PlayLesson"
    const val PLAYLISTADD = "PlaylistAdd"
    const val PLAYLISTADDCHECK = "PlaylistAddCheck"
    const val PLAYLISTADDCHECKCIRCLE = "PlaylistAddCheckCircle"
    const val PLAYLISTADDCIRCLE = "PlaylistAddCircle"
    const val PLAYLISTPLAY = "PlaylistPlay"
    const val PLAYLISTREMOVE = "PlaylistRemove"
    const val PLUMBING = "Plumbing"
    const val PLUSONE = "PlusOne"
    const val PODCASTS = "Podcasts"
    const val POINTOFSALE = "PointOfSale"
    const val POLICY = "Policy"
    const val POLL = "Poll"
    const val POLYLINE = "Polyline"
    const val POLYMER = "Polymer"
    const val POOL = "Pool"
    const val PORTABLEWIFIOFF = "PortableWifiOff"
    const val PORTRAIT = "Portrait"
    const val POSTADD = "PostAdd"
    const val POWER = "Power"
    const val POWERINPUT = "PowerInput"
    const val POWEROFF = "PowerOff"
    const val POWERSETTINGSNEW = "PowerSettingsNew"
    const val PRECISIONMANUFACTURING = "PrecisionManufacturing"
    const val PREGNANTWOMAN = "PregnantWoman"
    const val PRESENTTOALL = "PresentToAll"
    const val PREVIEW = "Preview"
    const val PRICECHANGE = "PriceChange"
    const val PRICECHECK = "PriceCheck"
    const val PRINT = "Print"
    const val PRINTDISABLED = "PrintDisabled"
    const val PRIORITYHIGH = "PriorityHigh"
    const val PRIVACYTIP = "PrivacyTip"
    const val PRIVATECONNECTIVITY = "PrivateConnectivity"
    const val PRODUCTIONQUANTITYLIMITS = "ProductionQuantityLimits"
    const val PROPANE = "Propane"
    const val PROPANETANK = "PropaneTank"
    const val PSYCHOLOGY = "Psychology"
    const val PSYCHOLOGYALT = "PsychologyAlt"
    const val PUBLIC = "Public"
    const val PUBLICOFF = "PublicOff"
    const val PUBLISH = "Publish"
    const val PUBLISHEDWITHCHANGES = "PublishedWithChanges"
    const val PUNCHCLOCK = "PunchClock"
    const val PUSHPIN = "PushPin"
    const val QRCODE = "QrCode"
    const val QRCODE2 = "QrCode2"
    const val QRCODESCANNER = "QrCodeScanner"
    const val QUERYBUILDER = "QueryBuilder"
    const val QUERYSTATS = "QueryStats"
    const val QUESTIONANSWER = "QuestionAnswer"
    const val QUESTIONMARK = "QuestionMark"
    const val QUEUE = "Queue"
    const val QUEUEMUSIC = "QueueMusic"
    const val QUEUEPLAYNEXT = "QueuePlayNext"
    const val QUICKREPLY = "Quickreply"
    const val QUIZ = "Quiz"
    const val RMOBILEDATA = "RMobiledata"
    const val RADAR = "Radar"
    const val RADIO = "Radio"
    const val RADIOBUTTONCHECKED = "RadioButtonChecked"
    const val RADIOBUTTONUNCHECKED = "RadioButtonUnchecked"
    const val RAILWAYALERT = "RailwayAlert"
    const val RAMENDINING = "RamenDining"
    const val RAMPLEFT = "RampLeft"
    const val RAMPRIGHT = "RampRight"
    const val RATEREVIEW = "RateReview"
    const val RAWOFF = "RawOff"
    const val RAWON = "RawOn"
    const val READMORE = "ReadMore"
    const val REALESTATEAGENT = "RealEstateAgent"
    const val RECEIPT = "Receipt"
    const val RECEIPTLONG = "ReceiptLong"
    const val RECENTACTORS = "RecentActors"
    const val RECOMMEND = "Recommend"
    const val RECORDVOICEOVER = "RecordVoiceOver"
    const val RECTANGLE = "Rectangle"
    const val RECYCLING = "Recycling"
    const val REDEEM = "Redeem"
    const val REDO = "Redo"
    const val REDUCECAPACITY = "ReduceCapacity"
    const val REFRESH = "Refresh"
    const val REMEMBERME = "RememberMe"
    const val REMOVE = "Remove"
    const val REMOVECIRCLE = "RemoveCircle"
    const val REMOVECIRCLEOUTLINE = "RemoveCircleOutline"
    const val REMOVEDONE = "RemoveDone"
    const val REMOVEFROMQUEUE = "RemoveFromQueue"
    const val REMOVEMODERATOR = "RemoveModerator"
    const val REMOVEREDEYE = "RemoveRedEye"
    const val REMOVEROAD = "RemoveRoad"
    const val REMOVESHOPPINGCART = "RemoveShoppingCart"
    const val REORDER = "Reorder"
    const val REPARTITION = "Repartition"
    const val REPEAT = "Repeat"
    const val REPEATON = "RepeatOn"
    const val REPEATONE = "RepeatOne"
    const val REPEATONEON = "RepeatOneOn"
    const val REPLAY = "Replay"
    const val REPLAY10 = "Replay10"
    const val REPLAY30 = "Replay30"
    const val REPLAY5 = "Replay5"
    const val REPLAYCIRCLEFILLED = "ReplayCircleFilled"
    const val REPLY = "Reply"
    const val REPLYALL = "ReplyAll"
    const val REPORT = "Report"
    const val REPORTGMAILERRORRED = "ReportGmailerrorred"
    const val REPORTOFF = "ReportOff"
    const val REPORTPROBLEM = "ReportProblem"
    const val REQUESTPAGE = "RequestPage"
    const val REQUESTQUOTE = "RequestQuote"
    const val RESETTV = "ResetTv"
    const val RESTARTALT = "RestartAlt"
    const val RESTAURANT = "Restaurant"
    const val RESTAURANTMENU = "RestaurantMenu"
    const val RESTORE = "Restore"
    const val RESTOREFROMTRASH = "RestoreFromTrash"
    const val RESTOREPAGE = "RestorePage"
    const val REVIEWS = "Reviews"
    const val RICEBOWL = "RiceBowl"
    const val RINGVOLUME = "RingVolume"
    const val ROCKET = "Rocket"
    const val ROCKETLAUNCH = "RocketLaunch"
    const val ROLLERSHADES = "RollerShades"
    const val ROLLERSHADESCLOSED = "RollerShadesClosed"
    const val ROLLERSKATING = "RollerSkating"
    const val ROOFING = "Roofing"
    const val ROOM = "Room"
    const val ROOMPREFERENCES = "RoomPreferences"
    const val ROOMSERVICE = "RoomService"
    const val ROTATE90DEGREESCCW = "Rotate90DegreesCcw"
    const val ROTATE90DEGREESCW = "Rotate90DegreesCw"
    const val ROTATELEFT = "RotateLeft"
    const val ROTATERIGHT = "RotateRight"
    const val ROUNDABOUTLEFT = "RoundaboutLeft"
    const val ROUNDABOUTRIGHT = "RoundaboutRight"
    const val ROUNDEDCORNER = "RoundedCorner"
    const val ROUTE = "Route"
    const val ROUTER = "Router"
    const val ROWING = "Rowing"
    const val RSSFEED = "RssFeed"
    const val RSVP = "Rsvp"
    const val RTT = "Rtt"
    const val RULE = "Rule"
    const val RULEFOLDER = "RuleFolder"
    const val RUNCIRCLE = "RunCircle"
    const val RUNNINGWITHERRORS = "RunningWithErrors"
    const val RVHOOKUP = "RvHookup"
    const val SAFETYCHECK = "SafetyCheck"
    const val SAFETYDIVIDER = "SafetyDivider"
    const val SAILING = "Sailing"
    const val SANITIZER = "Sanitizer"
    const val SATELLITE = "Satellite"
    const val SATELLITEALT = "SatelliteAlt"
    const val SAVE = "Save"
    const val SAVEALT = "SaveAlt"
    const val SAVEAS = "SaveAs"
    const val SAVEDSEARCH = "SavedSearch"
    const val SAVINGS = "Savings"
    const val SCALE = "Scale"
    const val SCANNER = "Scanner"
    const val SCATTERPLOT = "ScatterPlot"
    const val SCHEDULE = "Schedule"
    const val SCHEDULESEND = "ScheduleSend"
    const val SCHEMA = "Schema"
    const val SCHOOL = "School"
    const val SCIENCE = "Science"
    const val SCORE = "Score"
    const val SCOREBOARD = "Scoreboard"
    const val SCREENLOCKLANDSCAPE = "ScreenLockLandscape"
    const val SCREENLOCKPORTRAIT = "ScreenLockPortrait"
    const val SCREENLOCKROTATION = "ScreenLockRotation"
    const val SCREENROTATION = "ScreenRotation"
    const val SCREENROTATIONALT = "ScreenRotationAlt"
    const val SCREENSEARCHDESKTOP = "ScreenSearchDesktop"
    const val SCREENSHARE = "ScreenShare"
    const val SCREENSHOT = "Screenshot"
    const val SCREENSHOTMONITOR = "ScreenshotMonitor"
    const val SCUBADIVING = "ScubaDiving"
    const val SD = "Sd"
    const val SDCARD = "SdCard"
    const val SDCARDALERT = "SdCardAlert"
    const val SDSTORAGE = "SdStorage"
    const val SEARCH = "Search"
    const val SEARCHOFF = "SearchOff"
    const val SECURITY = "Security"
    const val SECURITYUPDATE = "SecurityUpdate"
    const val SECURITYUPDATEGOOD = "SecurityUpdateGood"
    const val SECURITYUPDATEWARNING = "SecurityUpdateWarning"
    const val SEGMENT = "Segment"
    const val SELECTALL = "SelectAll"
    const val SELFIMPROVEMENT = "SelfImprovement"
    const val SELL = "Sell"
    const val SEND = "Send"
    const val SENDANDARCHIVE = "SendAndArchive"
    const val SENDTIMEEXTENSION = "SendTimeExtension"
    const val SENDTOMOBILE = "SendToMobile"
    const val SENSORDOOR = "SensorDoor"
    const val SENSOROCCUPIED = "SensorOccupied"
    const val SENSORWINDOW = "SensorWindow"
    const val SENSORS = "Sensors"
    const val SENSORSOFF = "SensorsOff"
    const val SENTIMENTDISSATISFIED = "SentimentDissatisfied"
    const val SENTIMENTNEUTRAL = "SentimentNeutral"
    const val SENTIMENTSATISFIED = "SentimentSatisfied"
    const val SENTIMENTSATISFIEDALT = "SentimentSatisfiedAlt"
    const val SENTIMENTVERYDISSATISFIED = "SentimentVeryDissatisfied"
    const val SENTIMENTVERYSATISFIED = "SentimentVerySatisfied"
    const val SETMEAL = "SetMeal"
    const val SETTINGS = "Settings"
    const val SETTINGSACCESSIBILITY = "SettingsAccessibility"
    const val SETTINGSAPPLICATIONS = "SettingsApplications"
    const val SETTINGSBACKUPRESTORE = "SettingsBackupRestore"
    const val SETTINGSBLUETOOTH = "SettingsBluetooth"
    const val SETTINGSBRIGHTNESS = "SettingsBrightness"
    const val SETTINGSCELL = "SettingsCell"
    const val SETTINGSETHERNET = "SettingsEthernet"
    const val SETTINGSINPUTANTENNA = "SettingsInputAntenna"
    const val SETTINGSINPUTCOMPONENT = "SettingsInputComponent"
    const val SETTINGSINPUTCOMPOSITE = "SettingsInputComposite"
    const val SETTINGSINPUTHDMI = "SettingsInputHdmi"
    const val SETTINGSINPUTSVIDEO = "SettingsInputSvideo"
    const val SETTINGSOVERSCAN = "SettingsOverscan"
    const val SETTINGSPHONE = "SettingsPhone"
    const val SETTINGSPOWER = "SettingsPower"
    const val SETTINGSREMOTE = "SettingsRemote"
    const val SETTINGSSUGGEST = "SettingsSuggest"
    const val SETTINGSSYSTEMDAYDREAM = "SettingsSystemDaydream"
    const val SETTINGSVOICE = "SettingsVoice"
    const val SEVERECOLD = "SevereCold"
    const val SHAPELINE = "ShapeLine"
    const val SHARE = "Share"
    const val SHARELOCATION = "ShareLocation"
    const val SHIELD = "Shield"
    const val SHIELDMOON = "ShieldMoon"
    const val SHOP = "Shop"
    const val SHOP2 = "Shop2"
    const val SHOPTWO = "ShopTwo"
    const val SHOPPINGBAG = "ShoppingBag"
    const val SHOPPINGBASKET = "ShoppingBasket"
    const val SHOPPINGCART = "ShoppingCart"
    const val SHOPPINGCARTCHECKOUT = "ShoppingCartCheckout"
    const val SHORTTEXT = "ShortText"
    const val SHORTCUT = "Shortcut"
    const val SHOWCHART = "ShowChart"
    const val SHOWER = "Shower"
    const val SHUFFLE = "Shuffle"
    const val SHUFFLEON = "ShuffleOn"
    const val SHUTTERSPEED = "ShutterSpeed"
    const val SICK = "Sick"
    const val SIGNLANGUAGE = "SignLanguage"
    const val SIGNALCELLULAR0BAR = "SignalCellular0Bar"
    const val SIGNALCELLULAR4BAR = "SignalCellular4Bar"
    const val SIGNALCELLULARALT = "SignalCellularAlt"
    const val SIGNALCELLULARALT1BAR = "SignalCellularAlt1Bar"
    const val SIGNALCELLULARALT2BAR = "SignalCellularAlt2Bar"
    const val SIGNALCELLULARCONNECTEDNOINTERNET0BAR = "SignalCellularConnectedNoInternet0Bar"
    const val SIGNALCELLULARCONNECTEDNOINTERNET4BAR = "SignalCellularConnectedNoInternet4Bar"
    const val SIGNALCELLULARNOSIM = "SignalCellularNoSim"
    const val SIGNALCELLULARNODATA = "SignalCellularNodata"
    const val SIGNALCELLULARNULL = "SignalCellularNull"
    const val SIGNALCELLULAROFF = "SignalCellularOff"
    const val SIGNALWIFI0BAR = "SignalWifi0Bar"
    const val SIGNALWIFI4BAR = "SignalWifi4Bar"
    const val SIGNALWIFI4BARLOCK = "SignalWifi4BarLock"
    const val SIGNALWIFIBAD = "SignalWifiBad"
    const val SIGNALWIFICONNECTEDNOINTERNET4 = "SignalWifiConnectedNoInternet4"
    const val SIGNALWIFIOFF = "SignalWifiOff"
    const val SIGNALWIFISTATUSBAR4BAR = "SignalWifiStatusbar4Bar"
    const val SIGNALWIFISTATUSBARCONNECTEDNOINTERNET4 = "SignalWifiStatusbarConnectedNoInternet4"
    const val SIGNALWIFISTATUSBARNULL = "SignalWifiStatusbarNull"
    const val SIGNPOST = "Signpost"
    const val SIMCARD = "SimCard"
    const val SIMCARDALERT = "SimCardAlert"
    const val SIMCARDDOWNLOAD = "SimCardDownload"
    const val SINGLEBED = "SingleBed"
    const val SIP = "Sip"
    const val SKATEBOARDING = "Skateboarding"
    const val SKIPNEXT = "SkipNext"
    const val SKIPPREVIOUS = "SkipPrevious"
    const val SLEDDING = "Sledding"
    const val SLIDESHOW = "Slideshow"
    const val SLOWMOTIONVIDEO = "SlowMotionVideo"
    const val SMARTBUTTON = "SmartButton"
    const val SMARTDISPLAY = "SmartDisplay"
    const val SMARTSCREEN = "SmartScreen"
    const val SMARTTOY = "SmartToy"
    const val SMARTPHONE = "Smartphone"
    const val SMOKEFREE = "SmokeFree"
    const val SMOKINGROOMS = "SmokingRooms"
    const val SMS = "Sms"
    const val SMSFAILED = "SmsFailed"
    const val SNIPPETFOLDER = "SnippetFolder"
    const val SNOOZE = "Snooze"
    const val SNOWBOARDING = "Snowboarding"
    const val SNOWMOBILE = "Snowmobile"
    const val SNOWSHOEING = "Snowshoeing"
    const val SOAP = "Soap"
    const val SOCIALDISTANCE = "SocialDistance"
    const val SOLARPOWER = "SolarPower"
    const val SORT = "Sort"
    const val SORTBYALPHA = "SortByAlpha"
    const val SOS = "Sos"
    const val SOUPKITCHEN = "SoupKitchen"
    const val SOURCE = "Source"
    const val SOUTH = "South"
    const val SOUTHAMERICA = "SouthAmerica"
    const val SOUTHEAST = "SouthEast"
    const val SOUTHWEST = "SouthWest"
    const val SPA = "Spa"
    const val SPACEBAR = "SpaceBar"
    const val SPACEDASHBOARD = "SpaceDashboard"
    const val SPATIALAUDIO = "SpatialAudio"
    const val SPATIALAUDIOOFF = "SpatialAudioOff"
    const val SPATIALTRACKING = "SpatialTracking"
    const val SPEAKER = "Speaker"
    const val SPEAKERGROUP = "SpeakerGroup"
    const val SPEAKERNOTES = "SpeakerNotes"
    const val SPEAKERNOTESOFF = "SpeakerNotesOff"
    const val SPEAKERPHONE = "SpeakerPhone"
    const val SPEED = "Speed"
    const val SPELLCHECK = "Spellcheck"
    const val SPLITSCREEN = "Splitscreen"
    const val SPOKE = "Spoke"
    const val SPORTS = "Sports"
    const val SPORTSBAR = "SportsBar"
    const val SPORTSBASEBALL = "SportsBaseball"
    const val SPORTSBASKETBALL = "SportsBasketball"
    const val SPORTSCRICKET = "SportsCricket"
    const val SPORTSESPORTS = "SportsEsports"
    const val SPORTSFOOTBALL = "SportsFootball"
    const val SPORTSGOLF = "SportsGolf"
    const val SPORTSGYMNASTICS = "SportsGymnastics"
    const val SPORTSHANDBALL = "SportsHandball"
    const val SPORTSHOCKEY = "SportsHockey"
    const val SPORTSKABADDI = "SportsKabaddi"
    const val SPORTSMARTIALARTS = "SportsMartialArts"
    const val SPORTSMMA = "SportsMma"
    const val SPORTSMOTORSPORTS = "SportsMotorsports"
    const val SPORTSRUGBY = "SportsRugby"
    const val SPORTSSCORE = "SportsScore"
    const val SPORTSSOCCER = "SportsSoccer"
    const val SPORTSTENNIS = "SportsTennis"
    const val SPORTSVOLLEYBALL = "SportsVolleyball"
    const val SQUARE = "Square"
    const val SQUAREFOOT = "SquareFoot"
    const val SSIDCHART = "SsidChart"
    const val STACKEDBARCHART = "StackedBarChart"
    const val STACKEDLINECHART = "StackedLineChart"
    const val STADIUM = "Stadium"
    const val STAIRS = "Stairs"
    const val STAR = "Star"
    const val STARBORDER = "StarBorder"
    const val STARBORDERPURPLE500 = "StarBorderPurple500"
    const val STARHALF = "StarHalf"
    const val STAROUTLINE = "StarOutline"
    const val STARPURPLE500 = "StarPurple500"
    const val STARRATE = "StarRate"
    const val STARS = "Stars"
    const val START = "Start"
    const val STAYCURRENTLANDSCAPE = "StayCurrentLandscape"
    const val STAYCURRENTPORTRAIT = "StayCurrentPortrait"
    const val STAYPRIMARYLANDSCAPE = "StayPrimaryLandscape"
    const val STAYPRIMARYPORTRAIT = "StayPrimaryPortrait"
    const val STICKYNOTE2 = "StickyNote2"
    const val STOP = "Stop"
    const val STOPCIRCLE = "StopCircle"
    const val STOPSCREENSHARE = "StopScreenShare"
    const val STORAGE = "Storage"
    const val STORE = "Store"
    const val STOREMALLDIRECTORY = "StoreMallDirectory"
    const val STOREFRONT = "Storefront"
    const val STORM = "Storm"
    const val STRAIGHT = "Straight"
    const val STRAIGHTEN = "Straighten"
    const val STREAM = "Stream"
    const val STREETVIEW = "Streetview"
    const val STRIKETHROUGHS = "StrikethroughS"
    const val STROLLER = "Stroller"
    const val STYLE = "Style"
    const val SUBDIRECTORYARROWLEFT = "SubdirectoryArrowLeft"
    const val SUBDIRECTORYARROWRIGHT = "SubdirectoryArrowRight"
    const val SUBJECT = "Subject"
    const val SUBSCRIPT = "Subscript"
    const val SUBSCRIPTIONS = "Subscriptions"
    const val SUBTITLES = "Subtitles"
    const val SUBTITLESOFF = "SubtitlesOff"
    const val SUBWAY = "Subway"
    const val SUMMARIZE = "Summarize"
    const val SUPERSCRIPT = "Superscript"
    const val SUPERVISEDUSERCIRCLE = "SupervisedUserCircle"
    const val SUPERVISORACCOUNT = "SupervisorAccount"
    const val SUPPORT = "Support"
    const val SUPPORTAGENT = "SupportAgent"
    const val SURFING = "Surfing"
    const val SURROUNDSOUND = "SurroundSound"
    const val SWAPCALLS = "SwapCalls"
    const val SWAPHORIZ = "SwapHoriz"
    const val SWAPHORIZONTALCIRCLE = "SwapHorizontalCircle"
    const val SWAPVERT = "SwapVert"
    const val SWAPVERTICALCIRCLE = "SwapVerticalCircle"
    const val SWIPE = "Swipe"
    const val SWIPEDOWN = "SwipeDown"
    const val SWIPEDOWNALT = "SwipeDownAlt"
    const val SWIPELEFT = "SwipeLeft"
    const val SWIPELEFTALT = "SwipeLeftAlt"
    const val SWIPERIGHT = "SwipeRight"
    const val SWIPERIGHTALT = "SwipeRightAlt"
    const val SWIPEUP = "SwipeUp"
    const val SWIPEUPALT = "SwipeUpAlt"
    const val SWIPEVERTICAL = "SwipeVertical"
    const val SWITCHACCESSSHORTCUT = "SwitchAccessShortcut"
    const val SWITCHACCESSSHORTCUTADD = "SwitchAccessShortcutAdd"
    const val SWITCHACCOUNT = "SwitchAccount"
    const val SWITCHCAMERA = "SwitchCamera"
    const val SWITCHLEFT = "SwitchLeft"
    const val SWITCHRIGHT = "SwitchRight"
    const val SWITCHVIDEO = "SwitchVideo"
    const val SYNAGOGUE = "Synagogue"
    const val SYNC = "Sync"
    const val SYNCALT = "SyncAlt"
    const val SYNCDISABLED = "SyncDisabled"
    const val SYNCLOCK = "SyncLock"
    const val SYNCPROBLEM = "SyncProblem"
    const val SYSTEMSECURITYUPDATE = "SystemSecurityUpdate"
    const val SYSTEMSECURITYUPDATEGOOD = "SystemSecurityUpdateGood"
    const val SYSTEMSECURITYUPDATEWARNING = "SystemSecurityUpdateWarning"
    const val SYSTEMUPDATE = "SystemUpdate"
    const val SYSTEMUPDATEALT = "SystemUpdateAlt"
    const val TAB = "Tab"
    const val TABUNSELECTED = "TabUnselected"
    const val TABLEBAR = "TableBar"
    const val TABLECHART = "TableChart"
    const val TABLERESTAURANT = "TableRestaurant"
    const val TABLEROWS = "TableRows"
    const val TABLEVIEW = "TableView"
    const val TABLET = "Tablet"
    const val TABLETANDROID = "TabletAndroid"
    const val TABLETMAC = "TabletMac"
    const val TAG = "Tag"
    const val TAGFACES = "TagFaces"
    const val TAKEOUTDINING = "TakeoutDining"
    const val TAPANDPLAY = "TapAndPlay"
    const val TAPAS = "Tapas"
    const val TASK = "Task"
    const val TASKALT = "TaskAlt"
    const val TAXIALERT = "TaxiAlert"
    const val TEMPLEBUDDHIST = "TempleBuddhist"
    const val TEMPLEHINDU = "TempleHindu"
    const val TERMINAL = "Terminal"
    const val TERRAIN = "Terrain"
    const val TEXTDECREASE = "TextDecrease"
    const val TEXTFIELDS = "TextFields"
    const val TEXTFORMAT = "TextFormat"
    const val TEXTINCREASE = "TextIncrease"
    const val TEXTROTATEUP = "TextRotateUp"
    const val TEXTROTATEVERTICAL = "TextRotateVertical"
    const val TEXTROTATIONANGLEDOWN = "TextRotationAngledown"
    const val TEXTROTATIONANGLEUP = "TextRotationAngleup"
    const val TEXTROTATIONDOWN = "TextRotationDown"
    const val TEXTROTATIONNONE = "TextRotationNone"
    const val TEXTSNIPPET = "TextSnippet"
    const val TEXTSMS = "Textsms"
    const val TEXTURE = "Texture"
    const val THEATERCOMEDY = "TheaterComedy"
    const val THEATERS = "Theaters"
    const val THERMOSTAT = "Thermostat"
    const val THERMOSTATAUTO = "ThermostatAuto"
    const val THUMBDOWN = "ThumbDown"
    const val THUMBDOWNALT = "ThumbDownAlt"
    const val THUMBDOWNOFFALT = "ThumbDownOffAlt"
    const val THUMBUP = "ThumbUp"
    const val THUMBUPALT = "ThumbUpAlt"
    const val THUMBUPOFFALT = "ThumbUpOffAlt"
    const val THUMBSUPDOWN = "ThumbsUpDown"
    const val THUNDERSTORM = "Thunderstorm"
    const val TIMETOLEAVE = "TimeToLeave"
    const val TIMELAPSE = "Timelapse"
    const val TIMELINE = "Timeline"
    const val TIMER = "Timer"
    const val TIMER10 = "Timer10"
    const val TIMER10SELECT = "Timer10Select"
    const val TIMER3 = "Timer3"
    const val TIMER3SELECT = "Timer3Select"
    const val TIMEROFF = "TimerOff"
    const val TIPSANDUPDATES = "TipsAndUpdates"
    const val TIREREPAIR = "TireRepair"
    const val TITLE = "Title"
    const val TOC = "Toc"
    const val TODAY = "Today"
    const val TOGGLEOFF = "ToggleOff"
    const val TOGGLEON = "ToggleOn"
    const val TOKEN = "Token"
    const val TOLL = "Toll"
    const val TONALITY = "Tonality"
    const val TOPIC = "Topic"
    const val TORNADO = "Tornado"
    const val TOUCHAPP = "TouchApp"
    const val TOUR = "Tour"
    const val TOYS = "Toys"
    const val TRACKCHANGES = "TrackChanges"
    const val TRAFFIC = "Traffic"
    const val TRAIN = "Train"
    const val TRAM = "Tram"
    const val TRANSCRIBE = "Transcribe"
    const val TRANSFERWITHINASTATION = "TransferWithinAStation"
    const val TRANSFORM = "Transform"
    const val TRANSGENDER = "Transgender"
    const val TRANSITENTEREXIT = "TransitEnterexit"
    const val TRANSLATE = "Translate"
    const val TRAVELEXPLORE = "TravelExplore"
    const val TRENDINGDOWN = "TrendingDown"
    const val TRENDINGFLAT = "TrendingFlat"
    const val TRENDINGUP = "TrendingUp"
    const val TRIPORIGIN = "TripOrigin"
    const val TROUBLESHOOT = "Troubleshoot"
    const val TRY = "Try"
    const val TSUNAMI = "Tsunami"
    const val TTY = "Tty"
    const val TUNE = "Tune"
    const val TUNGSTEN = "Tungsten"
    const val TURNLEFT = "TurnLeft"
    const val TURNRIGHT = "TurnRight"
    const val TURNSHARPLEFT = "TurnSharpLeft"
    const val TURNSHARPRIGHT = "TurnSharpRight"
    const val TURNSLIGHTLEFT = "TurnSlightLeft"
    const val TURNSLIGHTRIGHT = "TurnSlightRight"
    const val TURNEDIN = "TurnedIn"
    const val TURNEDINNOT = "TurnedInNot"
    const val TV = "Tv"
    const val TVOFF = "TvOff"
    const val TWOWHEELER = "TwoWheeler"
    const val TYPESPECIMEN = "TypeSpecimen"
    const val UTURNLEFT = "UTurnLeft"
    const val UTURNRIGHT = "UTurnRight"
    const val UMBRELLA = "Umbrella"
    const val UNARCHIVE = "Unarchive"
    const val UNDO = "Undo"
    const val UNFOLDLESS = "UnfoldLess"
    const val UNFOLDLESSDOUBLE = "UnfoldLessDouble"
    const val UNFOLDMORE = "UnfoldMore"
    const val UNFOLDMOREDOUBLE = "UnfoldMoreDouble"
    const val UNPUBLISHED = "Unpublished"
    const val UNSUBSCRIBE = "Unsubscribe"
    const val UPCOMING = "Upcoming"
    const val UPDATE = "Update"
    const val UPDATEDISABLED = "UpdateDisabled"
    const val UPGRADE = "Upgrade"
    const val UPLOAD = "Upload"
    const val UPLOADFILE = "UploadFile"
    const val USB = "Usb"
    const val USBOFF = "UsbOff"
    const val VACCINES = "Vaccines"
    const val VAPEFREE = "VapeFree"
    const val VAPINGROOMS = "VapingRooms"
    const val VERIFIED = "Verified"
    const val VERIFIEDUSER = "VerifiedUser"
    const val VERTICALALIGNBOTTOM = "VerticalAlignBottom"
    const val VERTICALALIGNCENTER = "VerticalAlignCenter"
    const val VERTICALALIGNTOP = "VerticalAlignTop"
    const val VERTICALDISTRIBUTE = "VerticalDistribute"
    const val VERTICALSHADES = "VerticalShades"
    const val VERTICALSHADESCLOSED = "VerticalShadesClosed"
    const val VERTICALSPLIT = "VerticalSplit"
    const val VIBRATION = "Vibration"
    const val VIDEOCALL = "VideoCall"
    const val VIDEOCAMERABACK = "VideoCameraBack"
    const val VIDEOCAMERAFRONT = "VideoCameraFront"
    const val VIDEOCHAT = "VideoChat"
    const val VIDEOFILE = "VideoFile"
    const val VIDEOLABEL = "VideoLabel"
    const val VIDEOLIBRARY = "VideoLibrary"
    const val VIDEOSETTINGS = "VideoSettings"
    const val VIDEOSTABLE = "VideoStable"
    const val VIDEOCAM = "Videocam"
    const val VIDEOCAMOFF = "VideocamOff"
    const val VIDEOGAMEASSET = "VideogameAsset"
    const val VIDEOGAMEASSETOFF = "VideogameAssetOff"
    const val VIEWAGENDA = "ViewAgenda"
    const val VIEWARRAY = "ViewArray"
    const val VIEWCAROUSEL = "ViewCarousel"
    const val VIEWCOLUMN = "ViewColumn"
    const val VIEWCOMFY = "ViewComfy"
    const val VIEWCOMFYALT = "ViewComfyAlt"
    const val VIEWCOMPACT = "ViewCompact"
    const val VIEWCOMPACTALT = "ViewCompactAlt"
    const val VIEWCOZY = "ViewCozy"
    const val VIEWDAY = "ViewDay"
    const val VIEWHEADLINE = "ViewHeadline"
    const val VIEWINAR = "ViewInAr"
    const val VIEWKANBAN = "ViewKanban"
    const val VIEWLIST = "ViewList"
    const val VIEWMODULE = "ViewModule"
    const val VIEWQUILT = "ViewQuilt"
    const val VIEWSIDEBAR = "ViewSidebar"
    const val VIEWSTREAM = "ViewStream"
    const val VIEWTIMELINE = "ViewTimeline"
    const val VIEWWEEK = "ViewWeek"
    const val VIGNETTE = "Vignette"
    const val VILLA = "Villa"
    const val VISIBILITY = "Visibility"
    const val VISIBILITYOFF = "VisibilityOff"
    const val VOICECHAT = "VoiceChat"
    const val VOICEOVEROFF = "VoiceOverOff"
    const val VOICEMAIL = "Voicemail"
    const val VOLCANO = "Volcano"
    const val VOLUMEDOWN = "VolumeDown"
    const val VOLUMEMUTE = "VolumeMute"
    const val VOLUMEOFF = "VolumeOff"
    const val VOLUMEUP = "VolumeUp"
    const val VOLUNTEERACTIVISM = "VolunteerActivism"
    const val VPNKEY = "VpnKey"
    const val VPNKEYOFF = "VpnKeyOff"
    const val VPNLOCK = "VpnLock"
    const val VRPANO = "Vrpano"
    const val WALLET = "Wallet"
    const val WALLPAPER = "Wallpaper"
    const val WAREHOUSE = "Warehouse"
    const val WARNING = "Warning"
    const val WARNINGAMBER = "WarningAmber"
    const val WASH = "Wash"
    const val WATCH = "Watch"
    const val WATCHLATER = "WatchLater"
    const val WATCHOFF = "WatchOff"
    const val WATER = "Water"
    const val WATERDAMAGE = "WaterDamage"
    const val WATERDROP = "WaterDrop"
    const val WATERFALLCHART = "WaterfallChart"
    const val WAVES = "Waves"
    const val WAVINGHAND = "WavingHand"
    const val WBAUTO = "WbAuto"
    const val WBCLOUDY = "WbCloudy"
    const val WBINCANDESCENT = "WbIncandescent"
    const val WBIRIDESCENT = "WbIridescent"
    const val WBSHADE = "WbShade"
    const val WBSUNNY = "WbSunny"
    const val WBTWILIGHT = "WbTwilight"
    const val WC = "Wc"
    const val WEB = "Web"
    const val WEBASSET = "WebAsset"
    const val WEBASSETOFF = "WebAssetOff"
    const val WEBSTORIES = "WebStories"
    const val WEBHOOK = "Webhook"
    const val WEEKEND = "Weekend"
    const val WEST = "West"
    const val WHATSAPP = "Whatsapp"
    const val WHATSHOT = "Whatshot"
    const val WHEELCHAIRPICKUP = "WheelchairPickup"
    const val WHERETOVOTE = "WhereToVote"
    const val WIDGETS = "Widgets"
    const val WIDTHFULL = "WidthFull"
    const val WIDTHNORMAL = "WidthNormal"
    const val WIDTHWIDE = "WidthWide"
    const val WIFI = "Wifi"
    const val WIFI1BAR = "Wifi1Bar"
    const val WIFI2BAR = "Wifi2Bar"
    const val WIFICALLING = "WifiCalling"
    const val WIFICALLING3 = "WifiCalling3"
    const val WIFICHANNEL = "WifiChannel"
    const val WIFIFIND = "WifiFind"
    const val WIFILOCK = "WifiLock"
    const val WIFIOFF = "WifiOff"
    const val WIFIPASSWORD = "WifiPassword"
    const val WIFIPROTECTEDSETUP = "WifiProtectedSetup"
    const val WIFITETHERING = "WifiTethering"
    const val WIFITETHERINGERROR = "WifiTetheringError"
    const val WIFITETHERINGERRORROUNDED = "WifiTetheringErrorRounded"
    const val WIFITETHERINGOFF = "WifiTetheringOff"
    const val WINDPOWER = "WindPower"
    const val WINDOW = "Window"
    const val WINEBAR = "WineBar"
    const val WOMAN = "Woman"
    const val WOMAN2 = "Woman2"
    const val WORK = "Work"
    const val WORKHISTORY = "WorkHistory"
    const val WORKOFF = "WorkOff"
    const val WORKOUTLINE = "WorkOutline"
    const val WORKSPACEPREMIUM = "WorkspacePremium"
    const val WORKSPACES = "Workspaces"
    const val WRAPTEXT = "WrapText"
    const val WRONGLOCATION = "WrongLocation"
    const val WYSIWYG = "Wysiwyg"
    const val YARD = "Yard"
    const val YOUTUBESEARCHEDFOR = "YoutubeSearchedFor"
    const val ZOOMIN = "ZoomIn"
    const val ZOOMINMAP = "ZoomInMap"
    const val ZOOMOUT = "ZoomOut"
    const val ZOOMOUTMAP = "ZoomOutMap"
    
    // 图标类型常量
    const val TYPE_DEFAULT = "Default"
    const val TYPE_FILLED = "Filled"
    const val TYPE_OUTLINED = "Outlined"
    const val TYPE_ROUNDED = "Rounded"
    const val TYPE_SHARP = "Sharp"
    const val TYPE_TWOTONE = "TwoTone"
    const val TYPE_AUTOMIRROREDFILLED = "AutoMirroredFilled"
    const val TYPE_AUTOMIRROREDOUTLINED = "AutoMirroredOutlined"
    const val TYPE_AUTOMIRROREDROUNDED = "AutoMirroredRounded"
    const val TYPE_AUTOMIRROREDSHARP = "AutoMirroredSharp"
    const val TYPE_AUTOMIRROREDTWOTONE = "AutoMirroredTwoTone"
}
