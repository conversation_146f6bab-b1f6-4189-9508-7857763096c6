package com.addzero.kmp.api

import de.jensklingenberg.ktorfit.http.*

/**
 * Ktorfit接口 - 由KSP自动生成
 * 原始Controller: com.addzero.web.modules.biz_dotfiles.controller.BizDotfilesController
 * 基础路径: /bizDotfiles
 * 输出目录: /Users/<USER>/AquaProjects/addzero/shared/src/commonMain/kotlin/com/addzero/kmp/api
 */
interface BizDotfilesApi {

/**
 * page
 * HTTP方法: GET
 * 路径: /bizDotfiles/page
 * 返回类型: kotlin.Unit
 */
    @GET("/bizDotfiles/page")    suspend fun page(): kotlin.Unit

/**
 * save
 * HTTP方法: POST
 * 路径: /bizDotfiles/save
 * 返回类型: kotlin.Unit
 */
    @POST("/bizDotfiles/save")    suspend fun save(): kotlin.Unit

}