<!doctype html>
<html>
<head>
    <title>Scalar API Reference</title>
    <meta charset="utf-8" />
    <meta
            name="viewport"
            content="width=device-width, initial-scale=1" />
</head>

<body>
<div id="app"></div>

<!-- Load the Script -->
<script src="https://cdn.jsdelivr.net/npm/@scalar/api-reference"></script>

<!-- Initialize the Scalar API Reference -->
<script>
    Scalar.createApiReference('#app', {
        // The URL of the OpenAPI/Swagger document
        url: 'http://localhost:12344/openapi.yaml',
        // Avoid CORS issues
        proxyUrl: 'https://proxy.scalar.com',
        // Add CORS configuration
        corsProxy: true,
       // alternate
       // alternate、default、moon、purple、solarized、bluePlanet、saturn、kepler、mars、deepSpace、none
       theme: 'kepler'
        // theme: 'deepSpace'


    })
</script>
</body>
</html>
