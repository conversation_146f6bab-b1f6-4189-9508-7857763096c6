[{"tableChineseName": "<p>sys_dict</p>@authorzjarlin@date2024/11/27@constructor创建[SysDict]", "tableEnglishName": "sys_dict", "databaseType": "pg", "databaseName": "", "dto": [{"ktName": "dictName", "colName": "dict_name", "colType": "TEXT", "colComment": "字典名称", "colLength": "", "primaryKeyFlag": "null", "selfIncreasingFlag": "null", "nullableFlag": "NOT NULL", "ktType": "String"}, {"ktName": "dictCode", "colName": "dict_code", "colType": "TEXT", "colComment": "字典编码", "colLength": "", "primaryKeyFlag": "null", "selfIncreasingFlag": "null", "nullableFlag": "NOT NULL", "ktType": "String"}, {"ktName": "description", "colName": "description", "colType": "TEXT", "colComment": "描述", "colLength": "", "primaryKeyFlag": "null", "selfIncreasingFlag": "null", "nullableFlag": "NULL", "ktType": "String"}, {"ktName": "deleted", "colName": "deleted", "colType": "INTEGER", "colComment": "", "colLength": "", "primaryKeyFlag": "null", "selfIncreasingFlag": "null", "nullableFlag": "NOT NULL", "ktType": "Int"}]}, {"tableChineseName": "笔记实体类，用于表示笔记的基本信息和结构。该实体类映射到数据库表`biz_note`。", "tableEnglishName": "biz_note", "databaseType": "pg", "databaseName": "", "dto": [{"ktName": "parent", "colName": "parent", "colType": "TEXT", "colComment": "笔记的父节点，表示当前笔记的父笔记。通过{@linkManyToOne}注解与子笔记关联。@return父笔记，如果没有父笔记则返回null", "colLength": "", "primaryKeyFlag": "null", "selfIncreasingFlag": "null", "nullableFlag": "NULL", "ktType": "BizNote"}, {"ktName": "title", "colName": "title", "colType": "TEXT", "colComment": "标题", "colLength": "", "primaryKeyFlag": "null", "selfIncreasingFlag": "null", "nullableFlag": "NOT NULL", "ktType": "String"}, {"ktName": "content", "colName": "content", "colType": "TEXT", "colComment": "内容", "colLength": "", "primaryKeyFlag": "null", "selfIncreasingFlag": "null", "nullableFlag": "NOT NULL", "ktType": "String"}, {"ktName": "type", "colName": "type", "colType": "TEXT", "colComment": "类型1=markdown2=pdf3=word4=excel@return笔记类型", "colLength": "", "primaryKeyFlag": "null", "selfIncreasingFlag": "null", "nullableFlag": "NULL", "ktType": "String"}, {"ktName": "path", "colName": "path", "colType": "TEXT", "colComment": "笔记的路径@return笔记路径", "colLength": "", "primaryKeyFlag": "null", "selfIncreasingFlag": "null", "nullableFlag": "NULL", "ktType": "String"}, {"ktName": "fileUrl", "colName": "file_url", "colType": "TEXT", "colComment": "笔记关联的文件链接（可选）。", "colLength": "", "primaryKeyFlag": "null", "selfIncreasingFlag": "null", "nullableFlag": "NULL", "ktType": "String"}]}, {"tableChineseName": "<p>sys_dict_item</p>@authorz<PERSON>lin@date2024-09-16", "tableEnglishName": "sys_dict_item", "databaseType": "pg", "databaseName": "", "dto": [{"ktName": "itemText", "colName": "item_text", "colType": "TEXT", "colComment": "字典项文本", "colLength": "", "primaryKeyFlag": "null", "selfIncreasingFlag": "null", "nullableFlag": "NOT NULL", "ktType": "String"}, {"ktName": "itemValue", "colName": "item_value", "colType": "TEXT", "colComment": "字典项值", "colLength": "", "primaryKeyFlag": "null", "selfIncreasingFlag": "null", "nullableFlag": "NOT NULL", "ktType": "String"}, {"ktName": "description", "colName": "description", "colType": "TEXT", "colComment": "描述", "colLength": "", "primaryKeyFlag": "null", "selfIncreasingFlag": "null", "nullableFlag": "NULL", "ktType": "String"}, {"ktName": "sortOrder", "colName": "sort_order", "colType": "BIGINT", "colComment": "排序", "colLength": "", "primaryKeyFlag": "null", "selfIncreasingFlag": "null", "nullableFlag": "NULL", "ktType": "<PERSON>"}, {"ktName": "status", "colName": "status", "colType": "BIGINT", "colComment": "状态（1启用0不启用）", "colLength": "", "primaryKeyFlag": "null", "selfIncreasingFlag": "null", "nullableFlag": "NULL", "ktType": "<PERSON>"}]}, {"tableChineseName": "sys_dept", "tableEnglishName": "sys_dept", "databaseType": "pg", "databaseName": "", "dto": [{"ktName": "name", "colName": "name", "colType": "TEXT", "colComment": "部门编号", "colLength": "", "primaryKeyFlag": "null", "selfIncreasingFlag": "null", "nullableFlag": "NOT NULL", "ktType": "String"}]}, {"tableChineseName": "@authorz<PERSON><PERSON>@date2024/11/03@constructor创建[SysUser]", "tableEnglishName": "sys_user", "databaseType": "pg", "databaseName": "", "dto": [{"ktName": "phone", "colName": "phone", "colType": "TEXT", "colComment": "手机号", "colLength": "", "primaryKeyFlag": "null", "selfIncreasingFlag": "null", "nullableFlag": "NOT NULL", "ktType": "String"}, {"ktName": "username", "colName": "username", "colType": "TEXT", "colComment": "", "colLength": "", "primaryKeyFlag": "null", "selfIncreasingFlag": "null", "nullableFlag": "NOT NULL", "ktType": "String"}, {"ktName": "password", "colName": "password", "colType": "TEXT", "colComment": "密码", "colLength": "", "primaryKeyFlag": "null", "selfIncreasingFlag": "null", "nullableFlag": "NOT NULL", "ktType": "String"}, {"ktName": "avatar", "colName": "avatar", "colType": "TEXT", "colComment": "头像", "colLength": "", "primaryKeyFlag": "null", "selfIncreasingFlag": "null", "nullableFlag": "NULL", "ktType": "String"}, {"ktName": "nickname", "colName": "nickname", "colType": "TEXT", "colComment": "昵称", "colLength": "", "primaryKeyFlag": "null", "selfIncreasingFlag": "null", "nullableFlag": "NULL", "ktType": "String"}, {"ktName": "gender", "colName": "gender", "colType": "TEXT", "colComment": "性别0：男1=女2-未知", "colLength": "", "primaryKeyFlag": "null", "selfIncreasingFlag": "null", "nullableFlag": "NULL", "ktType": "EnumSysGender"}]}, {"tableChineseName": "<p>环境变量管理</p>@authorzjarlin@date2024-10-20", "tableEnglishName": "biz_dotfiles", "databaseType": "pg", "databaseName": "", "dto": [{"ktName": "osStructure", "colName": "os_structure", "colType": "TEXT", "colComment": "系统架构arm64=arm64x86=x86不限=不限", "colLength": "", "primaryKeyFlag": "null", "selfIncreasingFlag": "null", "nullableFlag": "NULL", "ktType": "EnumShellPlatforms"}, {"ktName": "defType", "colName": "def_type", "colType": "TEXT", "colComment": "定义类型alias=aliasexport=exportfunction=functionsh=shvar=var", "colLength": "", "primaryKeyFlag": "null", "selfIncreasingFlag": "null", "nullableFlag": "NOT NULL", "ktType": "EnumShellDefType"}, {"ktName": "name", "colName": "name", "colType": "TEXT", "colComment": "名称", "colLength": "", "primaryKeyFlag": "null", "selfIncreasingFlag": "null", "nullableFlag": "NOT NULL", "ktType": "String"}, {"ktName": "value", "colName": "value", "colType": "TEXT", "colComment": "值", "colLength": "", "primaryKeyFlag": "null", "selfIncreasingFlag": "null", "nullableFlag": "NOT NULL", "ktType": "String"}, {"ktName": "describtion", "colName": "describtion", "colType": "TEXT", "colComment": "注释", "colLength": "", "primaryKeyFlag": "null", "selfIncreasingFlag": "null", "nullableFlag": "NULL", "ktType": "String"}, {"ktName": "status", "colName": "status", "colType": "TEXT", "colComment": "状态1=启用0=未启用", "colLength": "", "primaryKeyFlag": "null", "selfIncreasingFlag": "null", "nullableFlag": "NOT NULL", "ktType": "EnumSysToggle"}, {"ktName": "fileUrl", "colName": "file_url", "colType": "TEXT", "colComment": "文件地址", "colLength": "", "primaryKeyFlag": "null", "selfIncreasingFlag": "null", "nullableFlag": "NULL", "ktType": "String"}, {"ktName": "location", "colName": "location", "colType": "TEXT", "colComment": "文件位置", "colLength": "", "primaryKeyFlag": "null", "selfIncreasingFlag": "null", "nullableFlag": "NULL", "ktType": "String"}]}, {"tableChineseName": "标签实体类，用于管理笔记的标签系统该实体类映射到数据库表`biz_tag`", "tableEnglishName": "biz_tag", "databaseType": "pg", "databaseName": "", "dto": [{"ktName": "name", "colName": "name", "colType": "TEXT", "colComment": "标签名称", "colLength": "", "primaryKeyFlag": "null", "selfIncreasingFlag": "null", "nullableFlag": "NOT NULL", "ktType": "String"}, {"ktName": "description", "colName": "description", "colType": "TEXT", "colComment": "标签描述", "colLength": "", "primaryKeyFlag": "null", "selfIncreasingFlag": "null", "nullableFlag": "NULL", "ktType": "String"}]}, {"tableChineseName": "<p>区域表</p>@authorzjarlin@date2025-02-26", "tableEnglishName": "sys_area", "databaseType": "pg", "databaseName": "", "dto": [{"ktName": "nodeType", "colName": "node_type", "colType": "TEXT", "colComment": "1省,2市,3区数据库列名:node_type数据类型:text可空:是默认值:NULL::charactervarying", "colLength": "", "primaryKeyFlag": "null", "selfIncreasingFlag": "null", "nullableFlag": "NULL", "ktType": "String"}, {"ktName": "name", "colName": "name", "colType": "TEXT", "colComment": "name数据库列名:name数据类型:text可空:是默认值:NULL::charactervarying", "colLength": "", "primaryKeyFlag": "null", "selfIncreasingFlag": "null", "nullableFlag": "NULL", "ktType": "String"}, {"ktName": "areaCode", "colName": "area_code", "colType": "TEXT", "colComment": "区域编码数据库列名:area_code数据类型:text可空:是默认值:NULL::charactervarying", "colLength": "", "primaryKeyFlag": "null", "selfIncreasingFlag": "null", "nullableFlag": "NULL", "ktType": "String"}]}, {"tableChineseName": "", "tableEnglishName": "sys_user_dept_mapping", "databaseType": "pg", "databaseName": "", "dto": [{"ktName": "", "colName": "sys_user_id", "colType": "bigint", "colComment": "", "colLength": "", "primaryKeyFlag": "", "selfIncreasingFlag": "", "nullableFlag": "NOT NULL", "ktType": ""}, {"ktName": "", "colName": "dept_id", "colType": "bigint", "colComment": "", "colLength": "", "primaryKeyFlag": "", "selfIncreasingFlag": "", "nullableFlag": "NOT NULL", "ktType": ""}]}, {"tableChineseName": "", "tableEnglishName": "biz_mapping", "databaseType": "pg", "databaseName": "", "dto": [{"ktName": "", "colName": "from_id", "colType": "bigint", "colComment": "", "colLength": "", "primaryKeyFlag": "", "selfIncreasingFlag": "", "nullableFlag": "NOT NULL", "ktType": ""}, {"ktName": "", "colName": "to_id", "colType": "bigint", "colComment": "", "colLength": "", "primaryKeyFlag": "", "selfIncreasingFlag": "", "nullableFlag": "NOT NULL", "ktType": ""}]}]