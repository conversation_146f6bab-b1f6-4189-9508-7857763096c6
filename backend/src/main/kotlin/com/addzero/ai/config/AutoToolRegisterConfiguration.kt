
package com.addzero.ai.config

import cn.hutool.extra.spring.SpringUtil
import org.springframework.ai.tool.ToolCallbackProvider
import org.springframework.ai.tool.annotation.Tool
import org.springframework.ai.tool.method.MethodToolCallbackProvider
import org.springframework.context.ApplicationContext
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.core.annotation.AnnotationUtils
import org.springframework.stereotype.Service

fun getFunctionObjects(): Array<Any?> {
    // 获取所有标记了@Service注解的Bean
    val applicationContext = SpringUtil.getApplicationContext()
    val serviceMap = applicationContext.getBeansWithAnnotation(Service::class.java)
    val toolObjects: MutableList<Any?> = ArrayList<Any?>()


    // 遍历所有Service Bean
    for (serviceBean in serviceMap.values) {
        var clazz: Class<*> = serviceBean.javaClass

        // 处理代理类
        if (clazz.getName().contains("$$")) {
            clazz = clazz.getSuperclass()
        }

        // 检查类中是否有@Tool注解的方法
        var hasToolMethod = false
        for (method in clazz.getDeclaredMethods()) {
            if (AnnotationUtils.findAnnotation<Tool?>(method, Tool::class.java) != null) {
                hasToolMethod = true
                break
            }
        }

        // 如果存在至少一个@Tool方法，则添加到工具对象列表
        if (hasToolMethod) {
            toolObjects.add(serviceBean)
        }
    }
    return toolObjects.toTypedArray()
}




/**
 * 自动扫描并注册带有@Tool注解的组件
 */
@Configuration
class AutoToolRegisterConfiguration (private val applicationContext: ApplicationContext) {
    private lateinit var toolObjects: MutableList<Any?>

    @Bean
    fun autoRegisterTools(): ToolCallbackProvider {
        val functionObjects = getFunctionObjects()
        // 构建并返回ToolCallbackProvider
        val build = MethodToolCallbackProvider.builder()
            .toolObjects(*functionObjects)
            .build()
        return build
    }
}


