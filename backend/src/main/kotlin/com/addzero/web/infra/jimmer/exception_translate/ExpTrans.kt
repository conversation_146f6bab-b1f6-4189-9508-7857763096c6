//import org.babyfish.jimmer.sql.exception.ExecutionException
//import org.babyfish.jimmer.sql.exception.SaveException
//import org.babyfish.jimmer.sql.runtime.ExceptionTranslator
//import org.springframework.stereotype.Component
//import java.lang.Exception
//
//@Component
//class NotUniqueExceptionTranslator :
//    ExceptionTranslator<ExecutionException> {
//    override fun translate(exception: ExecutionException, args: ExceptionTranslator.Args): Exception? {
//        println(exception)
//        return null
//    }
//
//
//}