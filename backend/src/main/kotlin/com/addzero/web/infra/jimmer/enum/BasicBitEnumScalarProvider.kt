//package com.addzero.web.infra.jimmer.enum
//
//import org.babyfish.jimmer.sql.runtime.ScalarProvider
//import org.springframework.stereotype.Component
//
//@Component
//class BasicBitEnumScalarProvider : ScalarProvider<List<BaseEnum>, String> {
//    override fun toScalar(sqlValue: String): List<BaseEnum>? {
//        println(sqlValue)
//
//        return emptyList()
////BasicEnumScalarProvider
//    }
//
//    override fun toSql(scalarValue: List<BaseEnum>): String? {
//        return ""
//    }
//
//
//}
