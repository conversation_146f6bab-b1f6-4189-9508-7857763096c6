import com.google.devtools.ksp.symbol.KSPropertyDeclaration
import strategy.SimpleStrategyManager

/**
 * 根据字段类型渲染不同模板
 * generateDifferentTypes
 *
 * 🚀 最终可靠版本：
 * 1. ✅ 性能优化：只解析一次 KSType，避免重复调用 property.type.resolve()
 * 2. ✅ 可靠性：不依赖object自动初始化，使用懒加载确保策略正确注册
 * 3. ✅ 简单明了：所有策略在一个地方管理，易于维护
 *
 * 💡 添加新字段类型只需：
 * - 创建新的策略文件，继承 RegistryStrategy
 * - 在 SimpleStrategyManager.strategies 列表中添加一行
 * - 完成！
 */
fun KSPropertyDeclaration.generateDifferentTypes(): String {
    return SimpleStrategyManager.generateCode(this)
}
