import com.google.devtools.ksp.symbol.KSPropertyDeclaration
import strategy.FormStrategyManager

/**
 * 根据字段类型渲染不同模板
 * generateDifferentTypes
 *
 * 🎯 自动注册策略版本：
 * 1. ✅ 性能优化：策略内部只解析一次 KSType
 * 2. ✅ 自动注册：每个策略自动往 strategies 里 add
 * 3. ✅ 简单扩展：添加新策略只需创建 object 并实现接口
 * 4. ✅ 零配置：无需手动管理策略列表
 */
fun KSPropertyDeclaration.generateDifferentTypes(): String {
    // 使用策略管理器生成代码
    return FormStrategyManager.generateCode(this)
}

/**
 * 生成手机号字段
 */
private fun generatePhoneField(property: KSPropertyDeclaration, type: KSType): String {
    val name = getPropertyCamelName(property)
    val label = getPropertyLabel(property)
    val isRequired = !type.isMarkedNullable
    val defaultValue = getDefaultValue(property)

    return """
        AddTextField(
            value = state.value.$name?.toString() ?: "",
            onValueChange = {
                state.value = state.value.copy($name = if (it.isBlank()) $defaultValue else it.parseObjectByKtx())
            },
            label = $label,
            isRequired = $isRequired,
            regexEnum = RegexEnum.PHONE
        )
    """.trimIndent()
}

/**
 * 生成邮箱字段
 */
private fun generateEmailField(property: KSPropertyDeclaration, type: KSType): String {
    val name = getPropertyCamelName(property)
    val label = getPropertyLabel(property)
    val isRequired = !type.isMarkedNullable
    val defaultValue = getDefaultValue(property)

    return """
        AddTextField(
            value = state.value.$name?.toString() ?: "",
            onValueChange = {
                state.value = state.value.copy($name = if (it.isBlank()) $defaultValue else it.parseObjectByKtx())
            },
            label = $label,
            isRequired = $isRequired,
            regexEnum = RegexEnum.EMAIL
        )
    """.trimIndent()
}

/**
 * 生成URL字段
 */
private fun generateUrlField(property: KSPropertyDeclaration, type: KSType): String {
    val name = getPropertyCamelName(property)
    val label = getPropertyLabel(property)
    val isRequired = !type.isMarkedNullable
    val defaultValue = getDefaultValue(property)

    return """
        AddTextField(
            value = state.value.$name?.toString() ?: "",
            onValueChange = {
                state.value = state.value.copy($name = if (it.isBlank()) $defaultValue else it.parseObjectByKtx())
            },
            label = $label,
            isRequired = $isRequired,
            regexEnum = RegexEnum.URL
        )
    """.trimIndent()
}

/**
 * 生成身份证字段
 */
private fun generateIdCardField(property: KSPropertyDeclaration, type: KSType): String {
    val name = getPropertyCamelName(property)
    val label = getPropertyLabel(property)
    val isRequired = !type.isMarkedNullable
    val defaultValue = getDefaultValue(property)

    return """
        AddTextField(
            value = state.value.$name?.toString() ?: "",
            onValueChange = {
                state.value = state.value.copy($name = if (it.isBlank()) $defaultValue else it.parseObjectByKtx())
            },
            label = $label,
            isRequired = $isRequired,
            regexEnum = RegexEnum.ID_CARD
        )
    """.trimIndent()
}

/**
 * 生成银行卡字段
 */
private fun generateBankCardField(property: KSPropertyDeclaration, type: KSType): String {
    val name = getPropertyCamelName(property)
    val label = getPropertyLabel(property)
    val isRequired = !type.isMarkedNullable
    val defaultValue = getDefaultValue(property)

    return """
        AddTextField(
            value = state.value.$name?.toString() ?: "",
            onValueChange = {
                state.value = state.value.copy($name = if (it.isBlank()) $defaultValue else it.parseObjectByKtx())
            },
            label = $label,
            isRequired = $isRequired,
            regexEnum = RegexEnum.BANK_CARD
        )
    """.trimIndent()
}

/**
 * 生成整数字段
 */
private fun generateIntegerField(property: KSPropertyDeclaration, type: KSType): String {
    val name = getPropertyCamelName(property)
    val label = getPropertyLabel(property)
    val isRequired = !type.isMarkedNullable
    val defaultValue = getDefaultValue(property)

    return """
        IntegerField(
            value = state.value.$name?.toString() ?: "",
            onValueChange = {
                state.value = state.value.copy($name = if (it.isBlank()) $defaultValue else it.parseObjectByKtx())
            },
            label = $label,
            isRequired = $isRequired
        )
    """.trimIndent()
}

/**
 * 生成小数字段
 */
private fun generateDecimalField(property: KSPropertyDeclaration, type: KSType): String {
    val name = getPropertyCamelName(property)
    val label = getPropertyLabel(property)
    val isRequired = !type.isMarkedNullable
    val defaultValue = getDefaultValue(property)

    return """
        DecimalField(
            value = state.value.$name?.toString() ?: "",
            onValueChange = {
                state.value = state.value.copy($name = if (it.isBlank()) $defaultValue else it.parseObjectByKtx())
            },
            label = $label,
            isRequired = $isRequired,
            decimalPlaces = 2
        )
    """.trimIndent()
}

/**
 * 生成日期字段
 */
private fun generateDateField(property: KSPropertyDeclaration, type: KSType): String {
    val name = getPropertyCamelName(property)
    val label = getPropertyLabel(property)
    val isRequired = !type.isMarkedNullable
    val defaultValue = getDefaultValue(property)

    return """
        AddTextField(
            value = state.value.$name?.toString() ?: "",
            onValueChange = {
                state.value = state.value.copy($name = if (it.isBlank()) $defaultValue else it.parseObjectByKtx())
            },
            label = $label,
            isRequired = $isRequired,
            regexEnum = RegexEnum.DATE
        )
    """.trimIndent()
}

/**
 * 生成布尔字段
 */
private fun generateBooleanField(property: KSPropertyDeclaration, type: KSType): String {
    val name = getPropertyCamelName(property)
    val label = getPropertyLabel(property)

    return """
        Switch(
            checked = state.value.$name ?: false,
            onCheckedChange = {
                state.value = state.value.copy($name = it)
            },
            text = $label
        )
    """.trimIndent()
}

/**
 * 生成默认字段
 */
private fun generateDefaultField(property: KSPropertyDeclaration, type: KSType): String {
    val name = getPropertyCamelName(property)
    val label = getPropertyLabel(property)
    val isRequired = !type.isMarkedNullable
    val defaultValue = getDefaultValue(property)

    return """
        AddTextField(
            value = state.value.$name?.toString() ?: "",
            onValueChange = {
                state.value = state.value.copy($name = if (it.isBlank()) $defaultValue else it.parseObjectByKtx())
            },
            label = $label,
            isRequired = $isRequired
        )
    """.trimIndent()
}
