import com.google.devtools.ksp.symbol.KSPropertyDeclaration
import com.google.devtools.ksp.symbol.KSType

/**
 * 根据字段类型渲染不同模板
 * generateDifferentTypes
 *
 * 🎯 最原始的when语句版本：
 * 1. ✅ 性能优化：只解析一次 KSType，避免重复调用 property.type.resolve()
 * 2. ✅ 简单直接：没有复杂的设计模式，一目了然
 * 3. ✅ 易于维护：所有逻辑在一个地方，修改方便
 * 4. ✅ 性能最佳：没有额外的抽象层开销
 */
fun KSPropertyDeclaration.generateDifferentTypes(): String {
    val type = this.type.resolve() // 只解析一次！
    val ktName = getKtName(this)
    val typeName = type.declaration.simpleName.asString()

    return when {
        // 金额字段
        ktName.contains("money", ignoreCase = true) ||
        ktName.contains("amount", ignoreCase = true) ||
        ktName.contains("price", ignoreCase = true) ||
        ktName.contains("金额", ignoreCase = true) ||
        ktName.contains("价格", ignoreCase = true) -> generateMoneyField(this, type)

        // 百分比字段
        ktName.contains("percentage", ignoreCase = true) ||
        ktName.contains("percent", ignoreCase = true) ||
        ktName.contains("rate", ignoreCase = true) ||
        ktName.contains("百分比", ignoreCase = true) ||
        ktName.contains("比率", ignoreCase = true) -> generatePercentageField(this, type)

        // 手机号字段
        ktName.contains("phone", ignoreCase = true) ||
        ktName.contains("mobile", ignoreCase = true) ||
        ktName.contains("tel", ignoreCase = true) ||
        ktName.contains("手机", ignoreCase = true) ||
        ktName.contains("电话", ignoreCase = true) -> generatePhoneField(this, type)

        // 邮箱字段
        ktName.contains("email", ignoreCase = true) ||
        ktName.contains("mail", ignoreCase = true) ||
        ktName.contains("邮箱", ignoreCase = true) ||
        ktName.contains("邮件", ignoreCase = true) -> generateEmailField(this, type)

        // URL字段
        ktName.contains("url", ignoreCase = true) ||
        ktName.contains("link", ignoreCase = true) ||
        ktName.contains("website", ignoreCase = true) ||
        ktName.contains("网址", ignoreCase = true) ||
        ktName.contains("链接", ignoreCase = true) -> generateUrlField(this, type)

        // 身份证字段
        ktName.contains("idcard", ignoreCase = true) ||
        ktName.contains("identity", ignoreCase = true) ||
        ktName.contains("身份证", ignoreCase = true) -> generateIdCardField(this, type)

        // 银行卡字段
        ktName.contains("bankcard", ignoreCase = true) ||
        ktName.contains("cardno", ignoreCase = true) ||
        ktName.contains("银行卡", ignoreCase = true) ||
        ktName.contains("卡号", ignoreCase = true) -> generateBankCardField(this, type)

        // 整数类型
        typeName in setOf("Int", "Long", "Short", "Byte") -> generateIntegerField(this, type)

        // 小数类型
        typeName in setOf("Double", "Float", "BigDecimal") -> generateDecimalField(this, type)

        // 日期字段
        ktName.contains("date", ignoreCase = true) ||
        ktName.contains("time", ignoreCase = true) ||
        ktName.contains("日期", ignoreCase = true) ||
        ktName.contains("时间", ignoreCase = true) ||
        typeName.contains("Date") ||
        typeName.contains("LocalDate") ||
        typeName.contains("LocalDateTime") -> generateDateField(this, type)

        // 布尔类型
        typeName == "Boolean" -> generateBooleanField(this, type)

        // 默认字段
        else -> generateDefaultField(this, type)
    }
}

// ==================== 工具方法 ====================

/**
 * 获取属性的Kotlin类型名称
 */
private fun getKtName(property: KSPropertyDeclaration): String {
    return property.annotations
        .firstOrNull { it.shortName.asString() == "KtName" }
        ?.arguments?.firstOrNull()?.value?.toString()
        ?: property.simpleName.asString()
}

/**
 * 获取属性的驼峰命名
 */
private fun getPropertyCamelName(property: KSPropertyDeclaration): String {
    return property.simpleName.asString()
}

/**
 * 获取属性标签
 */
private fun getPropertyLabel(property: KSPropertyDeclaration): String {
    val ktName = getKtName(property)
    return "\"$ktName\""
}

/**
 * 判断字段是否必填（基于类型的可空性）
 */
private fun isRequired(type: KSType): Boolean {
    return !type.isMarkedNullable
}

/**
 * 获取默认值
 */
private fun getDefaultValue(property: KSPropertyDeclaration): String {
    return "null"
}
