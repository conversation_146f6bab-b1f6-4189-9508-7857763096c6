import com.addzero.kmp.util.JlStrUtil.makeSurroundWith
import com.addzero.kmp.util.defaultValue
import com.addzero.kmp.util.ktName
import com.addzero.kmp.util.removeAnyQuote
import com.google.devtools.ksp.symbol.KSPropertyDeclaration
import com.google.devtools.ksp.symbol.KSType

/**
 * 根据字段类型渲染不同模板
 * generateDifferentTypes
 *
 * 🎯 最原始的when语句版本：
 * 1. ✅ 性能优化：只解析一次 KSType，避免重复调用 property.type.resolve()
 * 2. ✅ 简单直接：没有复杂的设计模式，一目了然
 * 3. ✅ 易于维护：所有逻辑在一个地方，修改方便
 * 4. ✅ 性能最佳：没有额外的抽象层开销
 */
fun KSPropertyDeclaration.generateDifferentTypes(): String {


    val prop = this
    val ktName = prop.ktName()
    val type = prop.type.resolve() // 属性的KSType
    val typeDecl = type.declaration // 属性类型的声明
    val typeName = typeDecl.simpleName.asString() // 属性类型的简单名称
//    val qualifiedName = typeDecl.qualifiedName?.asString() // 属性类型的全限定名

    // 获取属性在同构体中的字符串表示（可能带Iso后缀）
//        val typeStr = getIsoTypeString(type, outputDir, generatedIsoClasses, packageName)

    // 生成默认值
//        val defaultValue = prop.defaultValue()
    // 判断是否需要导入对于form来说,递归可能这里会生成无用的iso
    val pdoc = (prop.docString ?: ktName).removeAnyQuote().makeSurroundWith("\"")

    val bool = ktName.contains("money", ignoreCase = true) ||
            ktName.contains("amount", ignoreCase = true) ||
            ktName.contains("price", ignoreCase = true) ||
            ktName.contains("金额", ignoreCase = true) ||
            ktName.contains("价格", ignoreCase = true)
    return when {
        // 金额字段
        bool -> generateMoneyField(this, type)

        // 百分比字段
        ktName.contains("percentage", ignoreCase = true) ||
                ktName.contains("percent", ignoreCase = true) ||
                ktName.contains("rate", ignoreCase = true) ||
                ktName.contains("百分比", ignoreCase = true) ||
                ktName.contains("比率", ignoreCase = true) -> generatePercentageField(this, type)

        // 手机号字段
        ktName.contains("phone", ignoreCase = true) ||
                ktName.contains("mobile", ignoreCase = true) ||
                ktName.contains("tel", ignoreCase = true) ||
                ktName.contains("手机", ignoreCase = true) ||
                ktName.contains("电话", ignoreCase = true) -> generatePhoneField(this, type)

        // 邮箱字段
        ktName.contains("email", ignoreCase = true) ||
                ktName.contains("mail", ignoreCase = true) ||
                ktName.contains("邮箱", ignoreCase = true) ||
                ktName.contains("邮件", ignoreCase = true) -> generateEmailField(this, type)

        // URL字段
        ktName.contains("url", ignoreCase = true) ||
                ktName.contains("link", ignoreCase = true) ||
                ktName.contains("website", ignoreCase = true) ||
                ktName.contains("网址", ignoreCase = true) ||
                ktName.contains("链接", ignoreCase = true) -> generateUrlField(this, type)

        // 身份证字段
        ktName.contains("idcard", ignoreCase = true) ||
                ktName.contains("identity", ignoreCase = true) ||
                ktName.contains("身份证", ignoreCase = true) -> generateIdCardField(this, type)

        // 银行卡字段
        ktName.contains("bankcard", ignoreCase = true) ||
                ktName.contains("cardno", ignoreCase = true) ||
                ktName.contains("银行卡", ignoreCase = true) ||
                ktName.contains("卡号", ignoreCase = true) -> generateBankCardField(this, type)

        // 整数类型
        typeName in setOf("Int", "Long", "Short", "Byte") -> generateIntegerField(this, type)

        // 小数类型
        typeName in setOf("Double", "Float", "BigDecimal") -> generateDecimalField(this, type)

        // 日期字段
        ktName.contains("date", ignoreCase = true) ||
                ktName.contains("time", ignoreCase = true) ||
                ktName.contains("日期", ignoreCase = true) ||
                ktName.contains("时间", ignoreCase = true) ||
                typeName.contains("Date") ||
                typeName.contains("LocalDate") ||
                typeName.contains("LocalDateTime") -> generateDateField(this, type)

        // 布尔类型
        typeName == "Boolean" -> generateBooleanField(this, type)

        // 默认字段
        else -> generateDefaultField(this, type)
    }
}

// ==================== 工具方法 ====================

/**
 * 获取属性的Kotlin类型名称
 */
private fun getKtName(property: KSPropertyDeclaration): String {
    return property.annotations
        .firstOrNull { it.shortName.asString() == "KtName" }
        ?.arguments?.firstOrNull()?.value?.toString()
        ?: property.simpleName.asString()
}



/**
 * 获取默认值
 */
private fun getDefaultValue(property: KSPropertyDeclaration): String {
    return "null"
}

// ==================== 字段生成方法 ====================

/**
 * 生成金额字段
 */
private fun generateMoneyField(property: KSPropertyDeclaration, type: KSType): String {
    val name = getPropertyCamelName(property)
    val label = getPropertyLabel(property)
    val isRequired = !type.isMarkedNullable
    val defaultValue = property.defaultValue()

    return """
        MoneyField(
            value = state.value.$name?.toString() ?: "",
            onValueChange = {
                state.value = state.value.copy($name = if (it.isBlank()) $defaultValue else it.parseObjectByKtx())
            },
            label = $label,
            isRequired = $isRequired
        )
    """.trimIndent()
}

/**
 * 生成百分比字段
 */
private fun generatePercentageField(property: KSPropertyDeclaration, type: KSType): String {
    val name = getPropertyCamelName(property)
    val label = getPropertyLabel(property)
    property.docString ?: property.ktName()
    val isRequired = !type.isMarkedNullable
    val defaultValue = getDefaultValue(property)

    return """
        AddPercentageField(
            value = state.value.$name?.toString() ?: "",
            onValueChange = {
                state.value = state.value.copy($name = if (it.isBlank()) $defaultValue else it.parseObjectByKtx())
            },
            label = $label,
            isRequired = $isRequired
        )
    """.trimIndent()
}

/**
 * 生成手机号字段
 */
private fun generatePhoneField(property: KSPropertyDeclaration, type: KSType): String {
    val name = getPropertyCamelName(property)
    val label = getPropertyLabel(property)
    val isRequired = !type.isMarkedNullable
    val defaultValue = getDefaultValue(property)

    return """
        AddTextField(
            value = state.value.$name?.toString() ?: "",
            onValueChange = {
                state.value = state.value.copy($name = if (it.isBlank()) $defaultValue else it.parseObjectByKtx())
            },
            label = $label,
            isRequired = $isRequired,
            regexEnum = RegexEnum.PHONE
        )
    """.trimIndent()
}

/**
 * 生成邮箱字段
 */
private fun generateEmailField(property: KSPropertyDeclaration, type: KSType): String {
    val name = getPropertyCamelName(property)
    val label = getPropertyLabel(property)
    val isRequired = !type.isMarkedNullable
    val defaultValue = getDefaultValue(property)

    return """
        AddTextField(
            value = state.value.$name?.toString() ?: "",
            onValueChange = {
                state.value = state.value.copy($name = if (it.isBlank()) $defaultValue else it.parseObjectByKtx())
            },
            label = $label,
            isRequired = $isRequired,
            regexEnum = RegexEnum.EMAIL
        )
    """.trimIndent()
}

/**
 * 生成URL字段
 */
private fun generateUrlField(property: KSPropertyDeclaration, type: KSType): String {
    val name = getPropertyCamelName(property)
    val label = getPropertyLabel(property)
    val isRequired = !type.isMarkedNullable
    val defaultValue = getDefaultValue(property)

    return """
        AddTextField(
            value = state.value.$name?.toString() ?: "",
            onValueChange = {
                state.value = state.value.copy($name = if (it.isBlank()) $defaultValue else it.parseObjectByKtx())
            },
            label = $label,
            isRequired = $isRequired,
            regexEnum = RegexEnum.URL
        )
    """.trimIndent()
}

/**
 * 生成身份证字段
 */
private fun generateIdCardField(property: KSPropertyDeclaration, type: KSType): String {
    val name = getPropertyCamelName(property)
    val label = getPropertyLabel(property)
    val isRequired = !type.isMarkedNullable
    val defaultValue = getDefaultValue(property)

    return """
        AddTextField(
            value = state.value.$name?.toString() ?: "",
            onValueChange = {
                state.value = state.value.copy($name = if (it.isBlank()) $defaultValue else it.parseObjectByKtx())
            },
            label = $label,
            isRequired = $isRequired,
            regexEnum = RegexEnum.ID_CARD
        )
    """.trimIndent()
}

/**
 * 生成银行卡字段
 */
private fun generateBankCardField(property: KSPropertyDeclaration, type: KSType): String {
    val name = getPropertyCamelName(property)
    val label = getPropertyLabel(property)
    val isRequired = !type.isMarkedNullable
    val defaultValue = getDefaultValue(property)

    return """
        AddTextField(
            value = state.value.$name?.toString() ?: "",
            onValueChange = {
                state.value = state.value.copy($name = if (it.isBlank()) $defaultValue else it.parseObjectByKtx())
            },
            label = $label,
            isRequired = $isRequired,
            regexEnum = RegexEnum.BANK_CARD
        )
    """.trimIndent()
}

/**
 * 生成整数字段
 */
private fun generateIntegerField(property: KSPropertyDeclaration, type: KSType): String {
    val name = getPropertyCamelName(property)
    val label = getPropertyLabel(property)
    val isRequired = !type.isMarkedNullable
    val defaultValue = getDefaultValue(property)

    return """
        IntegerField(
            value = state.value.$name?.toString() ?: "",
            onValueChange = {
                state.value = state.value.copy($name = if (it.isBlank()) $defaultValue else it.parseObjectByKtx())
            },
            label = $label,
            isRequired = $isRequired
        )
    """.trimIndent()
}

/**
 * 生成小数字段
 */
private fun generateDecimalField(property: KSPropertyDeclaration, type: KSType): String {
    val name = getPropertyCamelName(property)
    val label = getPropertyLabel(property)
    val isRequired = !type.isMarkedNullable
    val defaultValue = getDefaultValue(property)

    return """
        DecimalField(
            value = state.value.$name?.toString() ?: "",
            onValueChange = {
                state.value = state.value.copy($name = if (it.isBlank()) $defaultValue else it.parseObjectByKtx())
            },
            label = $label,
            isRequired = $isRequired,
            decimalPlaces = 2
        )
    """.trimIndent()
}

/**
 * 生成日期字段
 */
private fun generateDateField(property: KSPropertyDeclaration, type: KSType): String {
    val name = getPropertyCamelName(property)
    val label = getPropertyLabel(property)
    val isRequired = !type.isMarkedNullable
    val defaultValue = getDefaultValue(property)

    return """
        AddTextField(
            value = state.value.$name?.toString() ?: "",
            onValueChange = {
                state.value = state.value.copy($name = if (it.isBlank()) $defaultValue else it.parseObjectByKtx())
            },
            label = $label,
            isRequired = $isRequired,
            regexEnum = RegexEnum.DATE
        )
    """.trimIndent()
}

/**
 * 生成布尔字段
 */
private fun generateBooleanField(property: KSPropertyDeclaration, type: KSType): String {
    val name = getPropertyCamelName(property)
    val label = getPropertyLabel(property)

    return """
        Switch(
            checked = state.value.$name ?: false,
            onCheckedChange = {
                state.value = state.value.copy($name = it)
            },
            text = $label
        )
    """.trimIndent()
}

/**
 * 生成默认字段
 */
private fun generateDefaultField(property: KSPropertyDeclaration, type: KSType): String {
    val name = getPropertyCamelName(property)
    val label = getPropertyLabel(property)
    val isRequired = !type.isMarkedNullable
    val defaultValue = getDefaultValue(property)

    return """
        AddTextField(
            value = state.value.$name?.toString() ?: "",
            onValueChange = {
                state.value = state.value.copy($name = if (it.isBlank()) $defaultValue else it.parseObjectByKtx())
            },
            label = $label,
            isRequired = $isRequired
        )
    """.trimIndent()
}
