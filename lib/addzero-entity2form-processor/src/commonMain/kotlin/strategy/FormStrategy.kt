package strategy

import com.addzero.kmp.util.JlStrUtil.makeSurroundWith
import com.addzero.kmp.util.defaultValue
import com.addzero.kmp.util.removeAnyQuote
import com.google.devtools.ksp.symbol.KSDeclaration
import com.google.devtools.ksp.symbol.KSPropertyDeclaration
import com.google.devtools.ksp.symbol.KSType
import strategy.impl.*

/**
 * 表单策略接口
 *
 * 🎯 自动注册策略模式：
 * 1. 每个策略自动往 strategies 里 add
 * 2. support 和 genCode 方法只需要 KSPropertyDeclaration 参数
 * 3. 简单的自注册机制
 */
interface FormStrategy() {

    /**
     * 优先级（数字越小优先级越高）
     */
     val priority: Int

    /**
     * 判断是否支持该属性
     */
     fun support(prop: KSPropertyDeclaration): Boolean

    /**
     * 生成代码
     */
     fun genCode(prop: KSPropertyDeclaration): String

}

/**
 * 策略管理器
 */
object FormStrategyManager {
    val strategies = listOf(
        MoneyStrategy,
        PercentageStrategy,
        PhoneStrategy,
        EmailStrategy,
        UrlStrategy,
        IdCardStrategy,
        BankCardStrategy,
        IntegerStrategy,
        DecimalStrategy,
        DateStrategy,
        BooleanStrategy,
        StringStrategy
    )

    /**
     * 生成代码
     */
    fun generateCode(property: KSPropertyDeclaration): String {

        println("策略有${strategies.size}个")

        // 按优先级排序
        val sortedStrategies = strategies.sortedBy { it.priority }

        // 找到第一个支持的策略
        val strategy = sortedStrategies.first { it.support() }
        return strategy.genCode(property)
    }
}
