package strategy

import com.google.devtools.ksp.symbol.KSPropertyDeclaration
import com.google.devtools.ksp.symbol.KSType

/**
 * 表单策略接口
 *
 * 🎯 自动注册策略模式：
 * 1. 每个策略自动往 strategies 里 add
 * 2. support 和 genCode 方法只需要 KSPropertyDeclaration 参数
 * 3. 简单的自注册机制
 */
interface FormStrategy {
    /**
     * 优先级（数字越小优先级越高）
     */
    val priority: Int

    /**
     * 判断是否支持该属性
     */
    fun support(ksPropertyDeclaration: KSPropertyDeclaration): Boolean

    /**
     * 生成代码
     */
    fun genCode(ksPropertyDeclaration: KSPropertyDeclaration): String

    /**
     * 注册策略
     */
    fun register() {
        FormStrategyManager.strategies.add(this)
    }
}

/**
 * 策略管理器
 */
object FormStrategyManager {

    /**
     * 所有策略列表（自动注册）
     */
    val strategies: MutableList<FormStrategy> = mutableListOf()

    /**
     * 生成代码
     */
    fun generateCode(property: KSPropertyDeclaration): String {
        // 按优先级排序
        val sortedStrategies = strategies.sortedBy { it.priority }

        // 找到第一个支持的策略
        val strategy = sortedStrategies.first { it.support(property) }
        return strategy.genCode(property)
    }
}

// ==================== 工具方法 ====================

/**
 * 获取属性的Kotlin类型名称
 */
fun getKtName(property: KSPropertyDeclaration): String {
    return property.annotations
        .firstOrNull { it.shortName.asString() == "KtName" }
        ?.arguments?.firstOrNull()?.value?.toString()
        ?: property.simpleName.asString()
}

/**
 * 获取属性的驼峰命名
 */
fun getPropertyCamelName(property: KSPropertyDeclaration): String {
    return property.simpleName.asString()
}

/**
 * 获取属性标签
 */
fun getPropertyLabel(property: KSPropertyDeclaration): String {
    val ktName = getKtName(property)
    return "\"$ktName\""
}

/**
 * 判断字段是否必填（基于类型的可空性）
 */
fun isRequired(property: KSPropertyDeclaration): Boolean {
    return !property.type.resolve().isMarkedNullable
}

/**
 * 获取默认值
 */
fun getDefaultValue(property: KSPropertyDeclaration): String {
    return "null"
}
