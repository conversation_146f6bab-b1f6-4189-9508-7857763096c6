package strategy

/**
 * 策略注册注解
 * 使用此注解标记的策略类会被自动发现和注册
 */
@Target(AnnotationTarget.CLASS)
@Retention(AnnotationRetention.RUNTIME)
annotation class AutoRegisterStrategy(
    /**
     * 策略优先级，数值越小优先级越高
     */
    val priority: Int = 100
)

/**
 * 策略自动发现和注册器
 */
object FormFieldStrategyRegistry {
    
    private val _strategies = mutableListOf<FormFieldStrategy>()
    private var _initialized = false
    
    /**
     * 获取所有已注册的策略，按优先级排序
     */
    val strategies: List<FormFieldStrategy>
        get() {
            if (!_initialized) {
                initializeStrategies()
                _initialized = true
            }
            return _strategies
        }
    
    /**
     * 手动注册策略（用于无法使用注解的场景）
     */
    fun registerStrategy(strategy: FormFieldStrategy) {
        _strategies.add(strategy)
        _strategies.sortBy { it.priority }
    }
    
    /**
     * 初始化策略（在KMP环境下手动注册，因为没有反射）
     */
    private fun initializeStrategies() {
        // 在KMP环境下，我们需要手动注册所有策略
        // 但这样做的好处是所有策略都在一个地方集中管理
        registerAllStrategies()
    }
    
    /**
     * 注册所有策略的地方
     * 新增策略只需要在这里添加一行即可
     */
    private fun registerAllStrategies() {
        // 自动注册所有策略实现
        strategy.impl.IntegerFieldStrategy().let(::registerStrategy)
        strategy.impl.DecimalFieldStrategy().let(::registerStrategy)
        strategy.impl.MoneyFieldStrategy().let(::registerStrategy)
        strategy.impl.PercentageFieldStrategy().let(::registerStrategy)
        strategy.impl.PhoneFieldStrategy().let(::registerStrategy)
        strategy.impl.EmailFieldStrategy().let(::registerStrategy)
        strategy.impl.UrlFieldStrategy().let(::registerStrategy)
        strategy.impl.DateFieldStrategy().let(::registerStrategy)
        strategy.impl.BooleanFieldStrategy().let(::registerStrategy)
        strategy.impl.DefaultFieldStrategy().let(::registerStrategy)
    }
}
