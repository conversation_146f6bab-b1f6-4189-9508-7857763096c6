package strategy

import com.google.devtools.ksp.symbol.KSPropertyDeclaration

/**
 * 字符串策略（默认策略）
 * 
 * 🎯 自动注册示例：
 * 1. 实现 FormStrategy 接口
 * 2. 在 object 初始化时自动调用 register()
 * 3. 无需手动管理策略列表
 */
object StringStrategy : FormStrategy {
    
    override val priority: Int = 999 // 最低优先级，作为默认策略
    
    init {
        // 自动注册
        register()
    }
    
    override fun support(ksPropertyDeclaration: KSPropertyDeclaration): Boolean {
        // 默认策略支持所有类型
        return true
    }
    
    override fun genCode(ksPropertyDeclaration: KSPropertyDeclaration): String {
        val name = getPropertyCamelName(ksPropertyDeclaration)
        val label = getPropertyLabel(ksPropertyDeclaration)
        val isRequired = isRequired(ksPropertyDeclaration)
        val defaultValue = getDefaultValue(ksPropertyDeclaration)
        
        return """
            AddTextField(
                value = state.value.$name?.toString() ?: "",
                onValueChange = {
                    state.value = state.value.copy($name = if (it.isBlank()) $defaultValue else it.parseObjectByKtx())
                },
                label = $label,
                isRequired = $isRequired
            )
        """.trimIndent()
    }
}

/**
 * 金额策略
 */
object MoneyStrategy : FormStrategy {
    
    override val priority: Int = 1
    
    init {
        register()
    }
    
    override fun support(ksPropertyDeclaration: KSPropertyDeclaration): Boolean {
        val ktName = getKtName(ksPropertyDeclaration)
        return ktName.contains("money", ignoreCase = true) ||
               ktName.contains("amount", ignoreCase = true) ||
               ktName.contains("price", ignoreCase = true) ||
               ktName.contains("金额", ignoreCase = true) ||
               ktName.contains("价格", ignoreCase = true)
    }
    
    override fun genCode(ksPropertyDeclaration: KSPropertyDeclaration): String {
        val name = getPropertyCamelName(ksPropertyDeclaration)
        val label = getPropertyLabel(ksPropertyDeclaration)
        val isRequired = isRequired(ksPropertyDeclaration)
        val defaultValue = getDefaultValue(ksPropertyDeclaration)
        
        return """
            MoneyField(
                value = state.value.$name?.toString() ?: "",
                onValueChange = {
                    state.value = state.value.copy($name = if (it.isBlank()) $defaultValue else it.parseObjectByKtx())
                },
                label = $label,
                isRequired = $isRequired
            )
        """.trimIndent()
    }
}

/**
 * 百分比策略
 */
object PercentageStrategy : FormStrategy {
    
    override val priority: Int = 2
    
    init {
        register()
    }
    
    override fun support(ksPropertyDeclaration: KSPropertyDeclaration): Boolean {
        val ktName = getKtName(ksPropertyDeclaration)
        return ktName.contains("percentage", ignoreCase = true) ||
               ktName.contains("percent", ignoreCase = true) ||
               ktName.contains("rate", ignoreCase = true) ||
               ktName.contains("百分比", ignoreCase = true) ||
               ktName.contains("比率", ignoreCase = true)
    }
    
    override fun genCode(ksPropertyDeclaration: KSPropertyDeclaration): String {
        val name = getPropertyCamelName(ksPropertyDeclaration)
        val label = getPropertyLabel(ksPropertyDeclaration)
        val isRequired = isRequired(ksPropertyDeclaration)
        val defaultValue = getDefaultValue(ksPropertyDeclaration)
        
        return """
            PercentageField(
                value = state.value.$name?.toString() ?: "",
                onValueChange = {
                    state.value = state.value.copy($name = if (it.isBlank()) $defaultValue else it.parseObjectByKtx())
                },
                label = $label,
                isRequired = $isRequired
            )
        """.trimIndent()
    }
}

/**
 * 手机号策略
 */
object PhoneStrategy : FormStrategy {
    
    override val priority: Int = 3
    
    init {
        register()
    }
    
    override fun support(ksPropertyDeclaration: KSPropertyDeclaration): Boolean {
        val ktName = getKtName(ksPropertyDeclaration)
        return ktName.contains("phone", ignoreCase = true) ||
               ktName.contains("mobile", ignoreCase = true) ||
               ktName.contains("tel", ignoreCase = true) ||
               ktName.contains("手机", ignoreCase = true) ||
               ktName.contains("电话", ignoreCase = true)
    }
    
    override fun genCode(ksPropertyDeclaration: KSPropertyDeclaration): String {
        val name = getPropertyCamelName(ksPropertyDeclaration)
        val label = getPropertyLabel(ksPropertyDeclaration)
        val isRequired = isRequired(ksPropertyDeclaration)
        val defaultValue = getDefaultValue(ksPropertyDeclaration)
        
        return """
            AddTextField(
                value = state.value.$name?.toString() ?: "",
                onValueChange = {
                    state.value = state.value.copy($name = if (it.isBlank()) $defaultValue else it.parseObjectByKtx())
                },
                label = $label,
                isRequired = $isRequired,
                regexEnum = RegexEnum.PHONE
            )
        """.trimIndent()
    }
}

/**
 * 邮箱策略
 */
object EmailStrategy : FormStrategy {
    
    override val priority: Int = 4
    
    init {
        register()
    }
    
    override fun support(ksPropertyDeclaration: KSPropertyDeclaration): Boolean {
        val ktName = getKtName(ksPropertyDeclaration)
        return ktName.contains("email", ignoreCase = true) ||
               ktName.contains("mail", ignoreCase = true) ||
               ktName.contains("邮箱", ignoreCase = true) ||
               ktName.contains("邮件", ignoreCase = true)
    }
    
    override fun genCode(ksPropertyDeclaration: KSPropertyDeclaration): String {
        val name = getPropertyCamelName(ksPropertyDeclaration)
        val label = getPropertyLabel(ksPropertyDeclaration)
        val isRequired = isRequired(ksPropertyDeclaration)
        val defaultValue = getDefaultValue(ksPropertyDeclaration)
        
        return """
            AddTextField(
                value = state.value.$name?.toString() ?: "",
                onValueChange = {
                    state.value = state.value.copy($name = if (it.isBlank()) $defaultValue else it.parseObjectByKtx())
                },
                label = $label,
                isRequired = $isRequired,
                regexEnum = RegexEnum.EMAIL
            )
        """.trimIndent()
    }
}

/**
 * 整数策略
 */
object IntegerStrategy : FormStrategy {
    
    override val priority: Int = 8
    
    init {
        register()
    }
    
    override fun support(ksPropertyDeclaration: KSPropertyDeclaration): Boolean {
        val typeName = ksPropertyDeclaration.type.resolve().declaration.simpleName.asString()
        return typeName in setOf("Int", "Long", "Short", "Byte")
    }
    
    override fun genCode(ksPropertyDeclaration: KSPropertyDeclaration): String {
        val name = getPropertyCamelName(ksPropertyDeclaration)
        val label = getPropertyLabel(ksPropertyDeclaration)
        val isRequired = isRequired(ksPropertyDeclaration)
        val defaultValue = getDefaultValue(ksPropertyDeclaration)
        
        return """
            IntegerField(
                value = state.value.$name?.toString() ?: "",
                onValueChange = {
                    state.value = state.value.copy($name = if (it.isBlank()) $defaultValue else it.parseObjectByKtx())
                },
                label = $label,
                isRequired = $isRequired
            )
        """.trimIndent()
    }
}

/**
 * 布尔策略
 */
object BooleanStrategy : FormStrategy {
    
    override val priority: Int = 11
    
    init {
        register()
    }
    
    override fun support(ksPropertyDeclaration: KSPropertyDeclaration): Boolean {
        val typeName = ksPropertyDeclaration.type.resolve().declaration.simpleName.asString()
        return typeName == "Boolean"
    }
    
    override fun genCode(ksPropertyDeclaration: KSPropertyDeclaration): String {
        val name = getPropertyCamelName(ksPropertyDeclaration)
        val label = getPropertyLabel(ksPropertyDeclaration)
        
        return """
            Switch(
                checked = state.value.$name ?: false,
                onCheckedChange = {
                    state.value = state.value.copy($name = it)
                },
                text = $label
            )
        """.trimIndent()
    }
}
