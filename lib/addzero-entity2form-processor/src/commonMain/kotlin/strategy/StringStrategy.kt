package strategy

import com.google.devtools.ksp.symbol.KSPropertyDeclaration



/**
 * 字符串策略（默认策略）
 *
 * 🎯 真正的自动注册：
 * 1. 继承 FormStrategy sealed class
 * 2. 在类加载时自动注册（通过父类init块）
 * 3. 无需手动管理策略列表
 */
object StringStrategy : FormStrategy() {

    override val priority: Int = 999 // 最低优先级，作为默认策略

    override fun support(ksPropertyDeclaration: KSPropertyDeclaration): Boolean {
        // 默认策略支持所有类型
        return true
    }

    override fun genCode(ksPropertyDeclaration: KSPropertyDeclaration): String {
        val name = getPropertyCamelName(ksPropertyDeclaration)
        val label = getPropertyLabel(ksPropertyDeclaration)
        val isRequired = isRequired(ksPropertyDeclaration)
        val defaultValue = getDefaultValue(ksPropertyDeclaration)

        return """
            AddTextField(
                value = state.value.$name?.toString() ?: "",
                onValueChange = {
                    state.value = state.value.copy($name = if (it.isBlank()) $defaultValue else it.parseObjectByKtx())
                },
                label = $label,
                isRequired = $isRequired
            )
        """.trimIndent()
    }
}

/**
 * 金额策略
 */
object MoneyStrategy : FormStrategy() {

    override val priority: Int = 1

    override fun support(ksPropertyDeclaration: KSPropertyDeclaration): Boolean {
        val ktName = getKtName(ksPropertyDeclaration)
        return ktName.contains("money", ignoreCase = true) ||
               ktName.contains("amount", ignoreCase = true) ||
               ktName.contains("price", ignoreCase = true) ||
               ktName.contains("金额", ignoreCase = true) ||
               ktName.contains("价格", ignoreCase = true)
    }

    override fun genCode(ksPropertyDeclaration: KSPropertyDeclaration): String {
        val name = getPropertyCamelName(ksPropertyDeclaration)
        val label = getPropertyLabel(ksPropertyDeclaration)
        val isRequired = isRequired(ksPropertyDeclaration)
        val defaultValue = getDefaultValue(ksPropertyDeclaration)

        return """
            MoneyField(
                value = state.value.$name?.toString() ?: "",
                onValueChange = {
                    state.value = state.value.copy($name = if (it.isBlank()) $defaultValue else it.parseObjectByKtx())
                },
                label = $label,
                isRequired = $isRequired
            )
        """.trimIndent()
    }
}

/**
 * 百分比策略
 */
object PercentageStrategy : FormStrategy() {

    override val priority: Int = 2

    override fun support(ksPropertyDeclaration: KSPropertyDeclaration): Boolean {
        val ktName = getKtName(ksPropertyDeclaration)
        return ktName.contains("percentage", ignoreCase = true) ||
               ktName.contains("percent", ignoreCase = true) ||
               ktName.contains("rate", ignoreCase = true) ||
               ktName.contains("百分比", ignoreCase = true) ||
               ktName.contains("比率", ignoreCase = true)
    }

    override fun genCode(ksPropertyDeclaration: KSPropertyDeclaration): String {
        val name = getPropertyCamelName(ksPropertyDeclaration)
        val label = getPropertyLabel(ksPropertyDeclaration)
        val isRequired = isRequired(ksPropertyDeclaration)
        val defaultValue = getDefaultValue(ksPropertyDeclaration)

        return """
            PercentageField(
                value = state.value.$name?.toString() ?: "",
                onValueChange = {
                    state.value = state.value.copy($name = if (it.isBlank()) $defaultValue else it.parseObjectByKtx())
                },
                label = $label,
                isRequired = $isRequired
            )
        """.trimIndent()
    }
}

/**
 * 手机号策略
 */
object PhoneStrategy : FormStrategy() {

    override val priority: Int = 3

    override fun support(ksPropertyDeclaration: KSPropertyDeclaration): Boolean {
        val ktName = getKtName(ksPropertyDeclaration)
        return ktName.contains("phone", ignoreCase = true) ||
               ktName.contains("mobile", ignoreCase = true) ||
               ktName.contains("tel", ignoreCase = true) ||
               ktName.contains("手机", ignoreCase = true) ||
               ktName.contains("电话", ignoreCase = true)
    }

    override fun genCode(ksPropertyDeclaration: KSPropertyDeclaration): String {
        val name = getPropertyCamelName(ksPropertyDeclaration)
        val label = getPropertyLabel(ksPropertyDeclaration)
        val isRequired = isRequired(ksPropertyDeclaration)
        val defaultValue = getDefaultValue(ksPropertyDeclaration)

        return """
            AddTextField(
                value = state.value.$name?.toString() ?: "",
                onValueChange = {
                    state.value = state.value.copy($name = if (it.isBlank()) $defaultValue else it.parseObjectByKtx())
                },
                label = $label,
                isRequired = $isRequired,
                regexEnum = RegexEnum.PHONE
            )
        """.trimIndent()
    }
}

/**
 * 邮箱策略
 */
object EmailStrategy : FormStrategy() {

    override val priority: Int = 4

    override fun support(ksPropertyDeclaration: KSPropertyDeclaration): Boolean {
        val ktName = getKtName(ksPropertyDeclaration)
        return ktName.contains("email", ignoreCase = true) ||
               ktName.contains("mail", ignoreCase = true) ||
               ktName.contains("邮箱", ignoreCase = true) ||
               ktName.contains("邮件", ignoreCase = true)
    }

    override fun genCode(ksPropertyDeclaration: KSPropertyDeclaration): String {
        val name = getPropertyCamelName(ksPropertyDeclaration)
        val label = getPropertyLabel(ksPropertyDeclaration)
        val isRequired = isRequired(ksPropertyDeclaration)
        val defaultValue = getDefaultValue(ksPropertyDeclaration)

        return """
            AddTextField(
                value = state.value.$name?.toString() ?: "",
                onValueChange = {
                    state.value = state.value.copy($name = if (it.isBlank()) $defaultValue else it.parseObjectByKtx())
                },
                label = $label,
                isRequired = $isRequired,
                regexEnum = RegexEnum.EMAIL
            )
        """.trimIndent()
    }
}

/**
 * 整数策略
 */
object IntegerStrategy : FormStrategy() {

    override val priority: Int = 8

    override fun support(ksPropertyDeclaration: KSPropertyDeclaration): Boolean {
        val typeName = ksPropertyDeclaration.type.resolve().declaration.simpleName.asString()
        return typeName in setOf("Int", "Long", "Short", "Byte")
    }

    override fun genCode(ksPropertyDeclaration: KSPropertyDeclaration): String {
        val name = getPropertyCamelName(ksPropertyDeclaration)
        val label = getPropertyLabel(ksPropertyDeclaration)
        val isRequired = isRequired(ksPropertyDeclaration)
        val defaultValue = getDefaultValue(ksPropertyDeclaration)

        return """
            IntegerField(
                value = state.value.$name?.toString() ?: "",
                onValueChange = {
                    state.value = state.value.copy($name = if (it.isBlank()) $defaultValue else it.parseObjectByKtx())
                },
                label = $label,
                isRequired = $isRequired
            )
        """.trimIndent()
    }
}

/**
 * URL策略
 */
object UrlStrategy : FormStrategy() {

    override val priority: Int = 5

    override fun support(ksPropertyDeclaration: KSPropertyDeclaration): Boolean {
        val ktName = getKtName(ksPropertyDeclaration)
        return ktName.contains("url", ignoreCase = true) ||
               ktName.contains("link", ignoreCase = true) ||
               ktName.contains("website", ignoreCase = true) ||
               ktName.contains("网址", ignoreCase = true) ||
               ktName.contains("链接", ignoreCase = true)
    }

    override fun genCode(ksPropertyDeclaration: KSPropertyDeclaration): String {
        val name = getPropertyCamelName(ksPropertyDeclaration)
        val label = getPropertyLabel(ksPropertyDeclaration)
        val isRequired = isRequired(ksPropertyDeclaration)
        val defaultValue = getDefaultValue(ksPropertyDeclaration)

        return """
            AddTextField(
                value = state.value.$name?.toString() ?: "",
                onValueChange = {
                    state.value = state.value.copy($name = if (it.isBlank()) $defaultValue else it.parseObjectByKtx())
                },
                label = $label,
                isRequired = $isRequired,
                regexEnum = RegexEnum.URL
            )
        """.trimIndent()
    }
}

/**
 * 身份证策略
 */
object IdCardStrategy : FormStrategy() {

    override val priority: Int = 6

    override fun support(ksPropertyDeclaration: KSPropertyDeclaration): Boolean {
        val ktName = getKtName(ksPropertyDeclaration)
        return ktName.contains("idcard", ignoreCase = true) ||
               ktName.contains("identity", ignoreCase = true) ||
               ktName.contains("身份证", ignoreCase = true)
    }

    override fun genCode(ksPropertyDeclaration: KSPropertyDeclaration): String {
        val name = getPropertyCamelName(ksPropertyDeclaration)
        val label = getPropertyLabel(ksPropertyDeclaration)
        val isRequired = isRequired(ksPropertyDeclaration)
        val defaultValue = getDefaultValue(ksPropertyDeclaration)

        return """
            AddTextField(
                value = state.value.$name?.toString() ?: "",
                onValueChange = {
                    state.value = state.value.copy($name = if (it.isBlank()) $defaultValue else it.parseObjectByKtx())
                },
                label = $label,
                isRequired = $isRequired,
                regexEnum = RegexEnum.ID_CARD
            )
        """.trimIndent()
    }
}

/**
 * 银行卡策略
 */
object BankCardStrategy : FormStrategy() {

    override val priority: Int = 7

    override fun support(ksPropertyDeclaration: KSPropertyDeclaration): Boolean {
        val ktName = getKtName(ksPropertyDeclaration)
        return ktName.contains("bankcard", ignoreCase = true) ||
               ktName.contains("cardno", ignoreCase = true) ||
               ktName.contains("银行卡", ignoreCase = true) ||
               ktName.contains("卡号", ignoreCase = true)
    }

    override fun genCode(ksPropertyDeclaration: KSPropertyDeclaration): String {
        val name = getPropertyCamelName(ksPropertyDeclaration)
        val label = getPropertyLabel(ksPropertyDeclaration)
        val isRequired = isRequired(ksPropertyDeclaration)
        val defaultValue = getDefaultValue(ksPropertyDeclaration)

        return """
            AddTextField(
                value = state.value.$name?.toString() ?: "",
                onValueChange = {
                    state.value = state.value.copy($name = if (it.isBlank()) $defaultValue else it.parseObjectByKtx())
                },
                label = $label,
                isRequired = $isRequired,
                regexEnum = RegexEnum.BANK_CARD
            )
        """.trimIndent()
    }
}

/**
 * 小数策略
 */
object DecimalStrategy : FormStrategy() {

    override val priority: Int = 9

    override fun support(ksPropertyDeclaration: KSPropertyDeclaration): Boolean {
        val typeName = ksPropertyDeclaration.type.resolve().declaration.simpleName.asString()
        return typeName in setOf("Double", "Float", "BigDecimal")
    }

    override fun genCode(ksPropertyDeclaration: KSPropertyDeclaration): String {
        val name = getPropertyCamelName(ksPropertyDeclaration)
        val label = getPropertyLabel(ksPropertyDeclaration)
        val isRequired = isRequired(ksPropertyDeclaration)
        val defaultValue = getDefaultValue(ksPropertyDeclaration)

        return """
            DecimalField(
                value = state.value.$name?.toString() ?: "",
                onValueChange = {
                    state.value = state.value.copy($name = if (it.isBlank()) $defaultValue else it.parseObjectByKtx())
                },
                label = $label,
                isRequired = $isRequired,
                decimalPlaces = 2
            )
        """.trimIndent()
    }
}

/**
 * 日期策略
 */
object DateStrategy : FormStrategy() {

    override val priority: Int = 10

    override fun support(ksPropertyDeclaration: KSPropertyDeclaration): Boolean {
        val ktName = getKtName(ksPropertyDeclaration)
        val typeName = ksPropertyDeclaration.type.resolve().declaration.simpleName.asString()

        return ktName.contains("date", ignoreCase = true) ||
               ktName.contains("time", ignoreCase = true) ||
               ktName.contains("日期", ignoreCase = true) ||
               ktName.contains("时间", ignoreCase = true) ||
               typeName.contains("Date") ||
               typeName.contains("LocalDate") ||
               typeName.contains("LocalDateTime")
    }

    override fun genCode(ksPropertyDeclaration: KSPropertyDeclaration): String {
        val name = getPropertyCamelName(ksPropertyDeclaration)
        val label = getPropertyLabel(ksPropertyDeclaration)
        val isRequired = isRequired(ksPropertyDeclaration)
        val defaultValue = getDefaultValue(ksPropertyDeclaration)

        return """
            AddTextField(
                value = state.value.$name?.toString() ?: "",
                onValueChange = {
                    state.value = state.value.copy($name = if (it.isBlank()) $defaultValue else it.parseObjectByKtx())
                },
                label = $label,
                isRequired = $isRequired,
                regexEnum = RegexEnum.DATE
            )
        """.trimIndent()
    }
}

/**
 * 布尔策略
 */
object BooleanStrategy : FormStrategy() {

    override val priority: Int = 11

    override fun support(ksPropertyDeclaration: KSPropertyDeclaration): Boolean {
        val typeName = ksPropertyDeclaration.type.resolve().declaration.simpleName.asString()
        return typeName == "Boolean"
    }

    override fun genCode(ksPropertyDeclaration: KSPropertyDeclaration): String {
        val name = getPropertyCamelName(ksPropertyDeclaration)
        val label = getPropertyLabel(ksPropertyDeclaration)

        return """
            Switch(
                checked = state.value.$name ?: false,
                onCheckedChange = {
                    state.value = state.value.copy($name = it)
                },
                text = $label
            )
        """.trimIndent()
    }
}
