package strategy

import com.addzero.kmp.util.JlStrUtil.makeSurroundWith
import com.addzero.kmp.util.defaultValue
import com.addzero.kmp.util.ktName
import com.addzero.kmp.util.removeAnyQuote
import com.addzero.kmp.util.toLowCamelCase
import com.google.devtools.ksp.symbol.KSPropertyDeclaration
import com.google.devtools.ksp.symbol.KSType

/**
 * 表单字段策略基类，提供通用方法
 */
abstract class BaseFormFieldStrategy : FormFieldStrategy {
    
    /**
     * 获取属性名称
     */
    protected fun getPropertyName(property: KSPropertyDeclaration): String {
        return property.simpleName.asString()
    }
    
    /**
     * 获取属性的驼峰命名
     */
    protected fun getPropertyCamelName(property: KSPropertyDeclaration): String {
        return getPropertyName(property).toLowCamelCase()
    }
    
    /**
     * 获取属性的文档标签
     */
    protected fun getPropertyLabel(property: KSPropertyDeclaration): String {
        val name = getPropertyName(property)
        return (property.docString ?: name).removeAnyQuote().makeSurroundWith("\"")
    }
    
    /**
     * 判断字段是否必填（根据可空性）
     */
    protected fun isRequired(type: KSType): Boolean {
        return !type.isMarkedNullable
    }
    
    /**
     * 获取属性的默认值
     */
    protected fun getDefaultValue(property: KSPropertyDeclaration): String {
        return property.defaultValue()
    }
    
    /**
     * 获取属性的Kotlin名称
     */
    protected fun getKtName(property: KSPropertyDeclaration): String {
        return property.ktName()
    }
    
    /**
     * 获取类型的简单名称
     */
    protected fun getTypeName(type: KSType): String {
        return type.declaration.simpleName.asString()
    }
}
