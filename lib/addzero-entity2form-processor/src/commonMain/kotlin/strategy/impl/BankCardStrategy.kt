package strategy.impl

import com.google.devtools.ksp.symbol.KSPropertyDeclaration
import strategy.FormStrategy
import strategy.getKtName
import strategy.getPropertyCamelName
import strategy.getPropertyLabel
import strategy.isRequired
import strategy.getDefaultValue

/**
 * 银行卡策略
 */
object BankCardStrategy : FormStrategy() {
    
    override val priority: Int = 7
    
    override fun support(ksPropertyDeclaration: KSPropertyDeclaration): Boolean {
        val ktName = getKtName(ksPropertyDeclaration)
        return ktName.contains("bankcard", ignoreCase = true) ||
               ktName.contains("cardno", ignoreCase = true) ||
               ktName.contains("银行卡", ignoreCase = true) ||
               ktName.contains("卡号", ignoreCase = true)
    }

    override fun genCode(ksPropertyDeclaration: KSPropertyDeclaration): String {
        val name = getPropertyCamelName(ksPropertyDeclaration)
        val label = getPropertyLabel(ksPropertyDeclaration)
        val isRequired = isRequired(ksPropertyDeclaration)
        val defaultValue = getDefaultValue(ksPropertyDeclaration)

        return """
            AddTextField(
                value = state.value.$name?.toString() ?: "",
                onValueChange = {
                    state.value = state.value.copy($name = if (it.isBlank()) $defaultValue else it.parseObjectByKtx())
                },
                label = $label,
                isRequired = $isRequired,
                regexEnum = RegexEnum.BANK_CARD
            )
        """.trimIndent()
    }
}
