package strategy.impl

import com.google.devtools.ksp.symbol.KSPropertyDeclaration
import com.google.devtools.ksp.symbol.KSType
import strategy.RegistryStrategy

/**
 * 布尔字段策略
 * 使用 object 实现自动注册
 */
object BooleanFieldStrategy : RegistryStrategy() {
    
    override val priority: Int = 25
    
    override fun canHandle(property: KSPropertyDeclaration, type: KSType): Boolean {
        return getTypeName(type) == "Boolean"
    }
    
    override fun generateCode(property: KSPropertyDeclaration, type: KSType): String {
        val name = getPropertyCamelName(property)
        val label = getPropertyLabel(property)
        
        return """
            Row(verticalAlignment = Alignment.CenterVertically) {
                Switch(
                    checked = state.value.$name ?: false,
                    onCheckedChange = {
                        state.value = state.value.copy($name = it)
                    }
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = if (state.value.$name == true) "是" else "否",
                    style = MaterialTheme.typography.bodyMedium
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = $label,
                    style = MaterialTheme.typography.bodyMedium
                )
            }
        """.trimIndent()
    }
}
