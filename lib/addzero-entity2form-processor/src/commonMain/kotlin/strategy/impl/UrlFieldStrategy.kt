package strategy.impl

import com.google.devtools.ksp.symbol.KSPropertyDeclaration
import com.google.devtools.ksp.symbol.KSType
import strategy.RegistryStrategy

/**
 * URL字段策略示例
 * 演示如何轻松添加新的字段类型处理
 * 使用 object 实现自动注册
 */
object UrlFieldStrategy : RegistryStrategy() {
    
    override val priority: Int = 9
    
    override fun canHandle(property: KSPropertyDeclaration, type: KSType): Boolean {
        val ktName = getKtName(property)
        return ktName.contains("url", ignoreCase = true) || 
               ktName.contains("link", ignoreCase = true) || 
               ktName.contains("website", ignoreCase = true)
    }
    
    override fun generateCode(property: KSPropertyDeclaration, type: KSType): String {
        val name = getPropertyCamelName(property)
        val label = getPropertyLabel(property)
        val isRequired = isRequired(type)
        val defaultValue = getDefaultValue(property)
        
        return """
            AddTextField(
                value = state.value.$name?.toString() ?: "",
                onValueChange = {
                    state.value = state.value.copy($name = if (it.isBlank()) $defaultValue else it.parseObjectByKtx())
                },
                label = $label,
                isRequired = $isRequired,
                regexValidator = RegexEnum.URL,
                leadingIcon = Icons.Default.Link,
                placeholder = "请输入网址"
            )
        """.trimIndent()
    }
}
