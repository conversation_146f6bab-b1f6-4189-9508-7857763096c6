package strategy.impl

import com.google.devtools.ksp.symbol.KSPropertyDeclaration
import com.google.devtools.ksp.symbol.KSType
import strategy.RegistryStrategy

/**
 * 日期字段策略
 * 使用 object 实现自动注册
 */
object DateFieldStrategy : RegistryStrategy() {
    
    override val priority: Int = 15
    
    override fun canHandle(property: KSPropertyDeclaration, type: KSType): Boolean {
        return getTypeName(type) in listOf("LocalDate", "LocalDateTime", "Date", "Instant")
    }
    
    override fun generateCode(property: KSPropertyDeclaration, type: KSType): String {
        val name = getPropertyCamelName(property)
        val label = getPropertyLabel(property)
        val isRequired = isRequired(type)
        val defaultValue = getDefaultValue(property)
        
        return """
            AddTextField(
                value = state.value.$name?.toString() ?: "",
                onValueChange = {
                    state.value = state.value.copy($name = if (it.isBlank()) $defaultValue else it.parseObjectByKtx())
                },
                label = $label,
                isRequired = $isRequired,
                regexValidator = RegexEnum.DATE,
                leadingIcon = Icons.Default.DateRange,
                placeholder = "请选择日期"
            )
        """.trimIndent()
    }
}
