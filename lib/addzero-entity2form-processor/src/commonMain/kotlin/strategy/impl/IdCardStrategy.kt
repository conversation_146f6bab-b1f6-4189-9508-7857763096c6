package strategy.impl

import com.google.devtools.ksp.symbol.KSPropertyDeclaration
import strategy.FormStrategy
import strategy.getKtName
import strategy.getPropertyCamelName
import strategy.getPropertyLabel
import strategy.isRequired
import strategy.getDefaultValue

/**
 * 身份证策略
 */
object IdCardStrategy : FormStrategy() {
    
    override val priority: Int = 6
    
    override fun support(ksPropertyDeclaration: KSPropertyDeclaration): Boolean {
        val ktName = getKtName(ksPropertyDeclaration)
        return ktName.contains("idcard", ignoreCase = true) ||
               ktName.contains("identity", ignoreCase = true) ||
               ktName.contains("身份证", ignoreCase = true)
    }

    override fun genCode(ksPropertyDeclaration: KSPropertyDeclaration): String {
        val name = getPropertyCamelName(ksPropertyDeclaration)
        val label = getPropertyLabel(ksPropertyDeclaration)
        val isRequired = isRequired(ksPropertyDeclaration)
        val defaultValue = getDefaultValue(ksPropertyDeclaration)

        return """
            AddTextField(
                value = state.value.$name?.toString() ?: "",
                onValueChange = {
                    state.value = state.value.copy($name = if (it.isBlank()) $defaultValue else it.parseObjectByKtx())
                },
                label = $label,
                isRequired = $isRequired,
                regexEnum = RegexEnum.ID_CARD
            )
        """.trimIndent()
    }
}
