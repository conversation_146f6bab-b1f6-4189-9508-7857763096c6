package strategy.impl

import com.google.devtools.ksp.symbol.KSPropertyDeclaration
import strategy.FormStrategy
import strategy.getKtName
import strategy.getPropertyCamelName
import strategy.getPropertyLabel
import strategy.isRequired
import strategy.getDefaultValue

/**
 * 邮箱策略
 */
object EmailStrategy : FormStrategy() {
    
    override val priority: Int = 4
    
    override fun support(ksPropertyDeclaration: KSPropertyDeclaration): Boolean {
        val ktName = getKtName(ksPropertyDeclaration)
        return ktName.contains("email", ignoreCase = true) ||
               ktName.contains("mail", ignoreCase = true) ||
               ktName.contains("邮箱", ignoreCase = true) ||
               ktName.contains("邮件", ignoreCase = true)
    }

    override fun genCode(ksPropertyDeclaration: KSPropertyDeclaration): String {
        val name = getPropertyCamelName(ksPropertyDeclaration)
        val label = getPropertyLabel(ksPropertyDeclaration)
        val isRequired = isRequired(ksPropertyDeclaration)
        val defaultValue = getDefaultValue(ksPropertyDeclaration)

        return """
            AddTextField(
                value = state.value.$name?.toString() ?: "",
                onValueChange = {
                    state.value = state.value.copy($name = if (it.isBlank()) $defaultValue else it.parseObjectByKtx())
                },
                label = $label,
                isRequired = $isRequired,
                regexEnum = RegexEnum.EMAIL
            )
        """.trimIndent()
    }
}
