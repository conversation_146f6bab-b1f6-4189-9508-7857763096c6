package strategy.impl

import com.google.devtools.ksp.symbol.KSPropertyDeclaration
import com.google.devtools.ksp.symbol.KSType
import strategy.BaseFormFieldStrategy

/**
 * 小数字段策略
 */
class DecimalFieldStrategy : BaseFormFieldStrategy() {
    
    override val priority: Int = 20
    
    override fun canHandle(property: KSPropertyDeclaration, type: KSType): Boolean {
        return getTypeName(type) in listOf("Double", "Float", "BigDecimal")
    }
    
    override fun generateCode(property: KSPropertyDeclaration, type: KSType): String {
        val name = getPropertyCamelName(property)
        val label = getPropertyLabel(property)
        val isRequired = isRequired(type)
        val defaultValue = getDefaultValue(property)
        
        return """
            AddDecimalField(
                value = state.value.$name?.toString() ?: "",
                onValueChange = {
                    state.value = state.value.copy($name = if (it.isBlank()) $defaultValue else it.toDoubleOrNull())
                },
                label = $label,
                isRequired = $isRequired
            )
        """.trimIndent()
    }
}
