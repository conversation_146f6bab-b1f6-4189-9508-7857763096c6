package strategy.impl

import com.google.devtools.ksp.symbol.KSPropertyDeclaration
import strategy.FormStrategy
import strategy.getKtName
import strategy.getPropertyCamelName
import strategy.getPropertyLabel
import strategy.isRequired
import strategy.getDefaultValue

/**
 * 百分比策略
 */
object PercentageStrategy : FormStrategy() {
    
    override val priority: Int = 2
    
    override fun support(ksPropertyDeclaration: KSPropertyDeclaration): Boolean {
        val ktName = getKtName(ksPropertyDeclaration)
        return ktName.contains("percentage", ignoreCase = true) ||
               ktName.contains("percent", ignoreCase = true) ||
               ktName.contains("rate", ignoreCase = true) ||
               ktName.contains("百分比", ignoreCase = true) ||
               ktName.contains("比率", ignoreCase = true)
    }

    override fun genCode(ksPropertyDeclaration: KSPropertyDeclaration): String {
        val name = getPropertyCamelName(ksPropertyDeclaration)
        val label = getPropertyLabel(ksPropertyDeclaration)
        val isRequired = isRequired(ksPropertyDeclaration)
        val defaultValue = getDefaultValue(ksPropertyDeclaration)

        return """
            PercentageField(
                value = state.value.$name?.toString() ?: "",
                onValueChange = {
                    state.value = state.value.copy($name = if (it.isBlank()) $defaultValue else it.parseObjectByKtx())
                },
                label = $label,
                isRequired = $isRequired
            )
        """.trimIndent()
    }
}
