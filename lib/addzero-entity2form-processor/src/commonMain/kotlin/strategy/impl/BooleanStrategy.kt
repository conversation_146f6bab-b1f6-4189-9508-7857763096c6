package strategy.impl

import com.google.devtools.ksp.symbol.KSPropertyDeclaration
import strategy.FormStrategy
import strategy.getPropertyCamelName
import strategy.getPropertyLabel

/**
 * 布尔策略
 */
object BooleanStrategy : FormStrategy() {
    
    override val priority: Int = 11
    
    override fun support(ksPropertyDeclaration: KSPropertyDeclaration): Boolean {
        val typeName = ksPropertyDeclaration.type.resolve().declaration.simpleName.asString()
        return typeName == "Boolean"
    }

    override fun genCode(ksPropertyDeclaration: KSPropertyDeclaration): String {
        val name = getPropertyCamelName(ksPropertyDeclaration)
        val label = getPropertyLabel(ksPropertyDeclaration)

        return """
            Switch(
                checked = state.value.$name ?: false,
                onCheckedChange = {
                    state.value = state.value.copy($name = it)
                },
                text = $label
            )
        """.trimIndent()
    }
}
