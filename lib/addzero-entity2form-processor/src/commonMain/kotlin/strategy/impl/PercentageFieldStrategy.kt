package strategy.impl

import com.google.devtools.ksp.symbol.KSPropertyDeclaration
import com.google.devtools.ksp.symbol.KSType
import strategy.RegistryStrategy

/**
 * 百分比字段策略
 * 使用 object 实现自动注册
 */
object PercentageFieldStrategy : RegistryStrategy() {
    
    override val priority: Int = 6 // 高优先级，优先于普通数字类型
    
    override fun canHandle(property: KSPropertyDeclaration, type: KSType): Boolean {
        val ktName = getKtName(property)
        return ktName.contains("percent", ignoreCase = true) || 
               ktName.contains("rate", ignoreCase = true) || 
               ktName.contains("ratio", ignoreCase = true)
    }
    
    override fun generateCode(property: KSPropertyDeclaration, type: KSType): String {
        val name = getPropertyCamelName(property)
        val label = getPropertyLabel(property)
        val isRequired = isRequired(type)
        val defaultValue = getDefaultValue(property)
        
        return """
            AddPercentageField(
                value = state.value.$name?.toString() ?: "",
                onValueChange = {
                    state.value = state.value.copy($name = if (it.isBlank()) $defaultValue else it.toDoubleOrNull())
                },
                label = $label,
                isRequired = $isRequired
            )
        """.trimIndent()
    }
}
