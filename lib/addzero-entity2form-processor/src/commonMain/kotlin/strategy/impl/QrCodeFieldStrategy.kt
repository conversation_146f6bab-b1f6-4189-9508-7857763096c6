package strategy.impl

import com.google.devtools.ksp.symbol.KSPropertyDeclaration
import com.google.devtools.ksp.symbol.KSType
import strategy.RegistryStrategy

/**
 * 二维码字段策略示例
 * 
 * 🎯 添加新策略步骤：
 * 1. 创建这个文件
 * 2. 继承 RegistryStrategy
 * 3. 实现 canHandle 和 generateCode 方法
 * 4. 在 SimpleStrategyManager.strategies 中添加 strategy.impl.QrCodeFieldStrategy
 * 5. 完成！
 */
object QrCodeFieldStrategy : RegistryStrategy() {
    
    override val priority: Int = 12
    
    override fun canHandle(property: KSPropertyDeclaration, type: KSType): Boolean {
        val ktName = getKtName(property)
        return ktName.contains("qrcode", ignoreCase = true) || 
               ktName.contains("qr", ignoreCase = true) || 
               ktName.contains("二维码", ignoreCase = true)
    }
    
    override fun generateCode(property: KSPropertyDeclaration, type: KSType): String {
        val name = getPropertyCamelName(property)
        val label = getPropertyLabel(property)
        val isRequired = isRequired(type)
        val defaultValue = getDefaultValue(property)
        
        return """
            AddTextField(
                value = state.value.$name?.toString() ?: "",
                onValueChange = {
                    state.value = state.value.copy($name = if (it.isBlank()) $defaultValue else it.parseObjectByKtx())
                },
                label = $label,
                isRequired = $isRequired,
                leadingIcon = Icons.Default.QrCode,
                placeholder = "请输入二维码内容",
                trailingIcon = {
                    IconButton(onClick = { /* 扫描二维码 */ }) {
                        Icon(Icons.Default.CameraAlt, contentDescription = "扫描二维码")
                    }
                }
            )
        """.trimIndent()
    }
}
