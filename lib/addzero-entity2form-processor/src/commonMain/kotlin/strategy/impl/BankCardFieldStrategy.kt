package strategy.impl

import com.google.devtools.ksp.symbol.KSPropertyDeclaration
import com.google.devtools.ksp.symbol.KSType
import strategy.RegistryStrategy

/**
 * 银行卡号字段策略
 * 
 * 🎯 零配置示例：
 * 1. 创建这个文件
 * 2. 继承 RegistryStrategy
 * 3. 实现方法
 * 4. 完成！真正的零配置！
 * 
 * ✨ 无需：
 * - 修改任何管理器文件
 * - 添加到初始化列表
 * - 手动注册
 * - 任何额外配置
 */
object BankCardFieldStrategy : RegistryStrategy() {
    
    override val priority: Int = 7
    
    override fun canHandle(property: KSPropertyDeclaration, type: KSType): Boolean {
        val ktName = getKtName(property)
        return ktName.contains("bankcard", ignoreCase = true) || 
               ktName.contains("cardno", ignoreCase = true) || 
               ktName.contains("bank", ignoreCase = true) ||
               ktName.contains("银行卡", ignoreCase = true)
    }
    
    override fun generateCode(property: KSPropertyDeclaration, type: KSType): String {
        val name = getPropertyCamelName(property)
        val label = getPropertyLabel(property)
        val isRequired = isRequired(type)
        val defaultValue = getDefaultValue(property)
        
        return """
            AddTextField(
                value = state.value.$name?.toString() ?: "",
                onValueChange = {
                    state.value = state.value.copy($name = if (it.isBlank()) $defaultValue else it.parseObjectByKtx())
                },
                label = $label,
                isRequired = $isRequired,
                regexValidator = RegexEnum.BANK_CARD,
                leadingIcon = Icons.Default.CreditCard,
                placeholder = "请输入银行卡号",
                keyboardType = KeyboardType.Number
            )
        """.trimIndent()
    }
}
