package strategy.impl

import com.google.devtools.ksp.symbol.KSPropertyDeclaration
import com.google.devtools.ksp.symbol.KSType
import strategy.AutoRegisterStrategy

/**
 * 电话字段策略
 * 使用 object 实现自动注册
 */
object PhoneFieldStrategy : AutoRegisterStrategy() {
    
    override val priority: Int = 7
    
    override fun canHandle(property: KSPropertyDeclaration, type: KSType): Boolean {
        return getKtName(property).contains("phone", ignoreCase = true)
    }
    
    override fun generateCode(property: KSPropertyDeclaration, type: KSType): String {
        val name = getPropertyCamelName(property)
        val label = getPropertyLabel(property)
        val isRequired = isRequired(type)
        val defaultValue = getDefaultValue(property)
        
        return """
            AddTextField(
                value = state.value.$name?.toString() ?: "",
                onValueChange = {
                    state.value = state.value.copy($name = if (it.isBlank()) $defaultValue else it.parseObjectByKtx())
                },
                label = $label,
                isRequired = $isRequired,
                regexValidator = RegexEnum.PHONE,
                leadingIcon = Icons.Default.Phone
                //  remoteValidationConfig = RemoteValidationConfig(
                //     tableName = "sys_user",
                //     column = "phone",
                // )
            )
        """.trimIndent()
    }
}
