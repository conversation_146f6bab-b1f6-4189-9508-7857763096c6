package strategy.impl

import com.google.devtools.ksp.symbol.KSPropertyDeclaration
import com.google.devtools.ksp.symbol.KSType
import strategy.BaseFormFieldStrategy

/**
 * 默认字段策略，处理所有其他类型
 */
class DefaultFieldStrategy : BaseFormFieldStrategy() {
    
    override val priority: Int = 999 // 最低优先级
    
    override fun canHandle(property: KSPropertyDeclaration, type: KSType): Boolean {
        return true // 总是可以处理，作为兜底策略
    }
    
    override fun generateCode(property: KSPropertyDeclaration, type: KSType): String {
        val name = getPropertyCamelName(property)
        val label = getPropertyLabel(property)
        val isRequired = isRequired(type)
        val defaultValue = getDefaultValue(property)
        
        return """
            AddTextField(
                value = state.value.$name?.toString() ?: "",
                onValueChange = {
                    state.value = state.value.copy($name = if (it.isBlank()) $defaultValue else it.parseObjectByKtx())
                },
                label = $label,
                isRequired = $isRequired
            )
        """.trimIndent()
    }
}
