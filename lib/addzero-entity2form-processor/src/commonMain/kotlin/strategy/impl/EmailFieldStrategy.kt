package strategy.impl

import com.google.devtools.ksp.symbol.KSPropertyDeclaration
import com.google.devtools.ksp.symbol.KSType
import strategy.AutoRegisterStrategy

/**
 * 邮箱字段策略
 * 使用 object 实现自动注册
 */
object EmailFieldStrategy : AutoRegisterStrategy() {
    
    override val priority: Int = 8
    
    override fun canHandle(property: KSPropertyDeclaration, type: KSType): Boolean {
        return getKtName(property).contains("email", ignoreCase = true)
    }
    
    override fun generateCode(property: KSPropertyDeclaration, type: KSType): String {
        val name = getPropertyCamelName(property)
        val label = getPropertyLabel(property)
        val isRequired = isRequired(type)
        val defaultValue = getDefaultValue(property)
        
        return """
            AddEmailField(
                value = state.value.$name?.toString() ?: "",
                onValueChange = {
                    state.value = state.value.copy($name = if (it.isBlank()) $defaultValue else it.parseObjectByKtx())
                },
                label = $label,
                isRequired = $isRequired
                //  disable = false,
                //   showCheckEmail = true,
                //        remoteValidationConfig = RemoteValidationConfig(
                //         tableName = "sys_user",
                //         column = "email",
                //    )
            )
        """.trimIndent()
    }
}
