package strategy.impl

import com.google.devtools.ksp.symbol.KSPropertyDeclaration
import com.google.devtools.ksp.symbol.KSType
import strategy.AutoRegisterStrategy

/**
 * 金额字段策略
 * 使用 object 实现自动注册
 */
object MoneyFieldStrategy : AutoRegisterStrategy() {
    
    override val priority: Int = 5 // 高优先级，优先于普通数字类型
    
    override fun canHandle(property: KSPropertyDeclaration, type: KSType): Boolean {
        val ktName = getKtName(property)
        return ktName.contains("money", ignoreCase = true) || 
               ktName.contains("amount", ignoreCase = true) || 
               ktName.contains("price", ignoreCase = true)
    }
    
    override fun generateCode(property: KSPropertyDeclaration, type: KSType): String {
        val name = getPropertyCamelName(property)
        val label = getPropertyLabel(property)
        val isRequired = isRequired(type)
        val defaultValue = getDefaultValue(property)
        
        return """
            AddMoneyField(
                value = state.value.$name?.toString() ?: "",
                onValueChange = {
                    state.value = state.value.copy($name = if (it.isBlank()) $defaultValue else it.toDoubleOrNull())
                },
                label = $label,
                currency = "¥",
                isRequired = $isRequired
            )
        """.trimIndent()
    }
}
