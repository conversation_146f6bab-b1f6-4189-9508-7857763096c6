package strategy.impl

import com.google.devtools.ksp.symbol.KSPropertyDeclaration
import strategy.FormStrategy
import strategy.getKtName
import strategy.getPropertyCamelName
import strategy.getPropertyLabel
import strategy.isRequired
import strategy.getDefaultValue

/**
 * 日期策略
 */
object DateStrategy : FormStrategy() {
    
    override val priority: Int = 10
    
    override fun support(ksPropertyDeclaration: KSPropertyDeclaration): Boolean {
        val ktName = getKtName(ksPropertyDeclaration)
        val typeName = ksPropertyDeclaration.type.resolve().declaration.simpleName.asString()
        
        return ktName.contains("date", ignoreCase = true) ||
               ktName.contains("time", ignoreCase = true) ||
               ktName.contains("日期", ignoreCase = true) ||
               ktName.contains("时间", ignoreCase = true) ||
               typeName.contains("Date") ||
               typeName.contains("LocalDate") ||
               typeName.contains("LocalDateTime")
    }

    override fun genCode(ksPropertyDeclaration: KSPropertyDeclaration): String {
        val name = getPropertyCamelName(ksPropertyDeclaration)
        val label = getPropertyLabel(ksPropertyDeclaration)
        val isRequired = isRequired(ksPropertyDeclaration)
        val defaultValue = getDefaultValue(ksPropertyDeclaration)

        return """
            AddTextField(
                value = state.value.$name?.toString() ?: "",
                onValueChange = {
                    state.value = state.value.copy($name = if (it.isBlank()) $defaultValue else it.parseObjectByKtx())
                },
                label = $label,
                isRequired = $isRequired,
                regexEnum = RegexEnum.DATE
            )
        """.trimIndent()
    }
}
