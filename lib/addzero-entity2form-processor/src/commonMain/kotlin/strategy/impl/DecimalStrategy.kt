package strategy.impl

import com.google.devtools.ksp.symbol.KSPropertyDeclaration
import strategy.FormStrategy
import strategy.getPropertyCamelName
import strategy.getPropertyLabel
import strategy.isRequired
import strategy.getDefaultValue

/**
 * 小数策略
 */
object DecimalStrategy : FormStrategy() {
    
    override val priority: Int = 9
    
    override fun support(ksPropertyDeclaration: KSPropertyDeclaration): Boolean {
        val typeName = ksPropertyDeclaration.type.resolve().declaration.simpleName.asString()
        return typeName in setOf("Double", "Float", "BigDecimal")
    }

    override fun genCode(ksPropertyDeclaration: KSPropertyDeclaration): String {
        val name = getPropertyCamelName(ksPropertyDeclaration)
        val label = getPropertyLabel(ksPropertyDeclaration)
        val isRequired = isRequired(ksPropertyDeclaration)
        val defaultValue = getDefaultValue(ksPropertyDeclaration)

        return """
            DecimalField(
                value = state.value.$name?.toString() ?: "",
                onValueChange = {
                    state.value = state.value.copy($name = if (it.isBlank()) $defaultValue else it.parseObjectByKtx())
                },
                label = $label,
                isRequired = $isRequired,
                decimalPlaces = 2
            )
        """.trimIndent()
    }
}
