package strategy.impl

import com.google.devtools.ksp.symbol.KSPropertyDeclaration
import com.google.devtools.ksp.symbol.KSType
import strategy.RegistryStrategy

/**
 * 身份证号码字段策略
 * 
 * 🎯 添加新策略就是这么简单！
 * 1. 创建这个文件
 * 2. 继承 RegistryStrategy
 * 3. 实现 canHandle 和 generateCode 方法
 * 4. 完成！无需修改任何其他文件
 */
object IdCardFieldStrategy : RegistryStrategy() {
    
    override val priority: Int = 8 // 设置优先级
    
    override fun canHandle(property: KSPropertyDeclaration, type: KSType): Boolean {
        val ktName = getKtName(property)
        return ktName.contains("idcard", ignoreCase = true) || 
               ktName.contains("idno", ignoreCase = true) || 
               ktName.contains("identity", ignoreCase = true) ||
               ktName.contains("身份证", ignoreCase = true)
    }
    
    override fun generateCode(property: KSPropertyDeclaration, type: KSType): String {
        val name = getPropertyCamelName(property)
        val label = getPropertyLabel(property)
        val isRequired = isRequired(type)
        val defaultValue = getDefaultValue(property)
        
        return """
            AddTextField(
                value = state.value.$name?.toString() ?: "",
                onValueChange = {
                    state.value = state.value.copy($name = if (it.isBlank()) $defaultValue else it.parseObjectByKtx())
                },
                label = $label,
                isRequired = $isRequired,
                regexValidator = RegexEnum.ID_CARD,
                leadingIcon = Icons.Default.Badge,
                placeholder = "请输入身份证号码",
                maxLength = 18
            )
        """.trimIndent()
    }
}
