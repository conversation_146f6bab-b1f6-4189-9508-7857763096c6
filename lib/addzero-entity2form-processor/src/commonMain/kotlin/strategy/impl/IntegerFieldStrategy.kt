package strategy.impl

import com.google.devtools.ksp.symbol.KSPropertyDeclaration
import com.google.devtools.ksp.symbol.KSType
import strategy.AutoRegisterStrategy

/**
 * 整数字段策略
 * 使用 object 实现自动注册
 */
object IntegerFieldStrategy : AutoRegisterStrategy() {
    
    override val priority: Int = 10
    
    override fun canHandle(property: KSPropertyDeclaration, type: KSType): Boolean {
        return getTypeName(type) in listOf("Int", "Long", "Short", "Byte")
    }
    
    override fun generateCode(property: KSPropertyDeclaration, type: KSType): String {
        val name = getPropertyCamelName(property)
        val label = getPropertyLabel(property)
        val isRequired = isRequired(type)
        val defaultValue = getDefaultValue(property)
        
        return """
            AddIntegerField(
                value = state.value.$name?.toString() ?: "",
                onValueChange = {
                    state.value = state.value.copy($name = if (it.isBlank()) $defaultValue else it.toIntOrNull())
                },
                label = $label,
                isRequired = $isRequired,
                allowNegative = true
            )
        """.trimIndent()
    }
}
