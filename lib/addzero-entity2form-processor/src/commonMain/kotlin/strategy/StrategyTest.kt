package strategy

/**
 * 策略注册测试
 * 用于验证策略是否正确注册
 */
object StrategyTest {
    
    /**
     * 测试策略注册
     */
    fun testStrategyRegistration() {
        val strategies = StrategyRegistry.getRegisteredStrategies()
        
        println("=== 策略注册测试 ===")
        println("已注册策略数量: ${strategies.size}")
        
        strategies.forEachIndexed { index, strategy ->
            println("${index + 1}. ${strategy::class.simpleName} (优先级: ${strategy.priority})")
        }
        
        if (strategies.isEmpty()) {
            println("❌ 策略注册失败！没有找到任何策略")
        } else {
            println("✅ 策略注册成功！")
        }
    }
}
