package strategy

import strategy.impl.*

/**
 * 策略加载器
 * 
 * 🎯 确保所有策略都被加载和注册：
 * 1. 通过引用所有策略object来触发类加载
 * 2. 类加载时会自动执行FormStrategy的init块
 * 3. 从而实现自动注册
 */
object StrategyLoader {
    
    /**
     * 加载所有策略
     */
    fun loadAllStrategies() {
        // 通过引用所有策略来触发类加载
        val strategies = listOf(
            MoneyStrategy,
            PercentageStrategy,
            PhoneStrategy,
            EmailStrategy,
            UrlStrategy,
            IdCardStrategy,
            BankCardStrategy,
            IntegerStrategy,
            DecimalStrategy,
            DateStrategy,
            BooleanStrategy,
            StringStrategy
        )
        
        // 访问每个策略的priority来确保完全加载
        strategies.forEach { it.priority }
        
        println("策略加载完成，共注册${FormStrategyManager.strategies.size}个策略")
    }
}
