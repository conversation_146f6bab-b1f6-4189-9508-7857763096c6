package strategy

import com.google.devtools.ksp.symbol.KSPropertyDeclaration
import com.google.devtools.ksp.symbol.KSType

/**
 * 策略工厂，使用构建器模式自动发现和管理策略
 */
object StrategyFactory {
    
    private val strategies = mutableListOf<FormFieldStrategy>()
    private var initialized = false
    
    /**
     * 策略构建器
     */
    class StrategyBuilder {
        private val tempStrategies = mutableListOf<FormFieldStrategy>()
        
        /**
         * 添加策略
         */
        inline fun <reified T : FormFieldStrategy> add(strategy: T): StrategyBuilder {
            tempStrategies.add(strategy)
            return this
        }
        
        /**
         * 构建并注册所有策略
         */
        fun build() {
            strategies.clear()
            strategies.addAll(tempStrategies.sortedBy { it.priority })
        }
    }
    
    /**
     * 初始化策略工厂
     */
    fun initialize(builder: StrategyBuilder.() -> Unit) {
        if (!initialized) {
            StrategyBuilder().apply(builder).build()
            initialized = true
        }
    }
    
    /**
     * 根据属性选择合适的策略并生成代码
     * 优化：只解析一次 KSType
     */
    fun generateCode(property: KSPropertyDeclaration): String {
        if (!initialized) {
            // 如果没有初始化，使用默认策略
            initializeDefaultStrategies()
        }
        
        val type = property.type.resolve() // 只解析一次
        
        val strategy = strategies.firstOrNull { it.canHandle(property, type) }
            ?: throw IllegalArgumentException("No strategy found for property: ${property.simpleName.asString()}")
        
        return strategy.generateCode(property, type)
    }
    
    /**
     * 默认策略初始化（向后兼容）
     */
    private fun initializeDefaultStrategies() {
        initialize {
            add(strategy.impl.MoneyFieldStrategy())
            add(strategy.impl.PercentageFieldStrategy())
            add(strategy.impl.PhoneFieldStrategy())
            add(strategy.impl.EmailFieldStrategy())
            add(strategy.impl.UrlFieldStrategy())
            add(strategy.impl.IntegerFieldStrategy())
            add(strategy.impl.DecimalFieldStrategy())
            add(strategy.impl.DateFieldStrategy())
            add(strategy.impl.BooleanFieldStrategy())
            add(strategy.impl.DefaultFieldStrategy())
        }
    }
}
