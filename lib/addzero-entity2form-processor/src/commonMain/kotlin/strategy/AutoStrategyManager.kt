package strategy

import com.google.devtools.ksp.symbol.KSPropertyDeclaration
import com.google.devtools.ksp.symbol.KSType

/**
 * 自动策略管理器
 * 使用自注册机制，策略会自动注册自己
 */
object AutoStrategyManager {
    
    private val strategies = mutableListOf<FormFieldStrategy>()
    
    /**
     * 策略自注册方法
     * 每个策略在初始化时调用此方法注册自己
     */
    fun registerStrategy(strategy: FormFieldStrategy) {
        strategies.add(strategy)
        strategies.sortBy { it.priority }
    }
    
    /**
     * 生成代码，优化性能：只解析一次 KSType
     */
    fun generateCode(property: KSPropertyDeclaration): String {
        // 确保策略已初始化
        StrategyInitializer.initialize()

        val type = property.type.resolve() // 只解析一次

        val strategy = strategies.firstOrNull { it.canHandle(property, type) }
            ?: throw IllegalArgumentException("No strategy found for property: ${property.simpleName.asString()}")

        return strategy.generateCode(property, type)
    }
    
    /**
     * 获取所有已注册的策略（用于调试）
     */
    fun getRegisteredStrategies(): List<FormFieldStrategy> = strategies.toList()
}

/**
 * 自注册策略基类
 * 继承此类的策略会自动注册到管理器中
 */
abstract class AutoRegisterStrategy : BaseFormFieldStrategy() {
    
    init {
        AutoStrategyManager.registerStrategy(this)
    }
}
