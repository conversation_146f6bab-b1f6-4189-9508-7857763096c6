package strategy

import com.google.devtools.ksp.symbol.KSPropertyDeclaration
import com.google.devtools.ksp.symbol.KSType

/**
 * 表单字段生成策略接口
 */
interface FormFieldStrategy {
    /**
     * 判断是否可以处理该属性
     */
    fun canHandle(property: KSPropertyDeclaration, type: KSType): Boolean
    
    /**
     * 生成表单字段代码
     */
    fun generateCode(property: KSPropertyDeclaration, type: KSType): String
    
    /**
     * 策略优先级，数值越小优先级越高
     */
    val priority: Int get() = 100
}
