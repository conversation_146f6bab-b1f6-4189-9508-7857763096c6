package strategy

import com.google.devtools.ksp.symbol.KSPropertyDeclaration
import com.google.devtools.ksp.symbol.KSType
import strategy.impl.*

/**
 * 表单字段策略管理器
 */
object FormFieldStrategyManager {
    
    private val strategies = listOf(
        // 按优先级排序，优先级高的在前面
        IntegerFieldStrategy(),
        DecimalFieldStrategy(),
        MoneyFieldStrategy(),
        PercentageFieldStrategy(),
        PhoneFieldStrategy(),
        EmailFieldStrategy(),
        UrlFieldStrategy(),
        DateFieldStrategy(),
        BooleanFieldStrategy(),
        DefaultFieldStrategy() // 默认策略放在最后
    ).sortedBy { it.priority }
    
    /**
     * 根据属性类型选择合适的策略并生成代码
     */
    fun generateCode(property: KSPropertyDeclaration, type: KSType): String {
        val strategy = strategies.firstOrNull { it.canHandle(property, type) }
            ?: throw IllegalArgumentException("No strategy found for property: ${property.simpleName.asString()}")
        
        return strategy.generateCode(property, type)
    }
}
