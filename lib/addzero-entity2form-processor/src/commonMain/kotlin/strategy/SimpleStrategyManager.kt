package strategy

import com.google.devtools.ksp.symbol.KSPropertyDeclaration
import com.google.devtools.ksp.symbol.KSType

/**
 * 简单策略管理器 - 最可靠的解决方案
 * 
 * 🎯 解决方案特点：
 * 1. 性能优化：只解析一次 KSType
 * 2. 可靠性：不依赖object的自动初始化
 * 3. 简单明了：所有策略在一个地方管理
 * 4. 易于扩展：添加新策略只需在这里添加一行
 */
object SimpleStrategyManager {
    
    /**
     * 所有策略的懒加载列表
     * 按优先级排序，优先级低的数字在前面
     */
    private val strategies: List<FormFieldStrategy> by lazy {
        listOf(
            // 特殊字段类型（高优先级）
            strategy.impl.MoneyFieldStrategy,
            strategy.impl.PercentageFieldStrategy,
            strategy.impl.PhoneFieldStrategy,
            strategy.impl.EmailFieldStrategy,
            strategy.impl.UrlFieldStrategy,
            strategy.impl.IdCardFieldStrategy,
            strategy.impl.BankCardFieldStrategy,

            // 基础数据类型
            strategy.impl.IntegerFieldStrategy,
            strategy.impl.QrCodeFieldStrategy,
            strategy.impl.DecimalFieldStrategy,
            strategy.impl.DateFieldStrategy,
            strategy.impl.BooleanFieldStrategy,

            // 默认策略（最低优先级）
            strategy.impl.DefaultFieldStrategy
        ).sortedBy { it.priority }
    }
    
    /**
     * 生成代码 - 高性能版本
     * 只解析一次 KSType，避免性能浪费
     */
    fun generateCode(property: KSPropertyDeclaration): String {
        val type = property.type.resolve() // 只解析一次！
        
        val strategy = strategies.firstOrNull { it.canHandle(property, type) }
            ?: throw IllegalArgumentException("No strategy found for property: ${property.simpleName.asString()}")
        
        return strategy.generateCode(property, type)
    }
    
    /**
     * 获取已注册策略（调试用）
     */
    fun getRegisteredStrategies(): List<FormFieldStrategy> = strategies
    
    /**
     * 打印策略信息（调试用）
     */
    fun printStrategies() {
        println("=== 策略列表 ===")
        println("已注册策略数量: ${strategies.size}")
        strategies.forEachIndexed { index, strategy ->
            println("${index + 1}. ${strategy::class.simpleName} (优先级: ${strategy.priority})")
        }
    }
}
