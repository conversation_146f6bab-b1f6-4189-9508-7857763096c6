# 表单字段策略模式

## 概述

本模块采用策略模式重构了原来的大型 `when` 语句，将不同类型字段的处理逻辑拆分到独立的策略类中，符合开闭原则。

## 架构设计

### 核心接口

- **FormFieldStrategy**: 策略接口，定义了字段处理的基本契约
- **BaseFormFieldStrategy**: 抽象基类，提供通用的工具方法
- **FormFieldStrategyManager**: 策略管理器，负责选择合适的策略

### 策略实现

每种字段类型都有对应的策略实现：

| 策略类 | 优先级 | 处理类型 | 说明 |
|--------|--------|----------|------|
| MoneyFieldStrategy | 5 | 金额字段 | 字段名包含 money/amount/price |
| PercentageFieldStrategy | 6 | 百分比字段 | 字段名包含 percent/rate/ratio |
| PhoneFieldStrategy | 7 | 电话字段 | 字段名包含 phone |
| EmailFieldStrategy | 8 | 邮箱字段 | 字段名包含 email |
| UrlFieldStrategy | 9 | URL字段 | 字段名包含 url/link/website |
| IntegerFieldStrategy | 10 | 整数类型 | Int, Long, Short, Byte |
| DateFieldStrategy | 15 | 日期类型 | LocalDate, LocalDateTime, Date, Instant |
| DecimalFieldStrategy | 20 | 小数类型 | Double, Float, BigDecimal |
| BooleanFieldStrategy | 25 | 布尔类型 | Boolean |
| DefaultFieldStrategy | 999 | 默认处理 | 兜底策略，处理其他所有类型 |

## 如何添加新的字段类型

### 1. 创建策略类

```kotlin
package strategy.impl

import com.google.devtools.ksp.symbol.KSPropertyDeclaration
import com.google.devtools.ksp.symbol.KSType
import strategy.BaseFormFieldStrategy

class YourFieldStrategy : BaseFormFieldStrategy() {
    
    override val priority: Int = 50 // 设置优先级
    
    override fun canHandle(property: KSPropertyDeclaration, type: KSType): Boolean {
        // 判断是否可以处理该字段
        return getKtName(property).contains("yourKeyword", ignoreCase = true)
    }
    
    override fun generateCode(property: KSPropertyDeclaration, type: KSType): String {
        val name = getPropertyCamelName(property)
        val label = getPropertyLabel(property)
        val isRequired = isRequired(type)
        val defaultValue = getDefaultValue(property)
        
        return """
            YourCustomField(
                value = state.value.$name?.toString() ?: "",
                onValueChange = {
                    state.value = state.value.copy($name = if (it.isBlank()) $defaultValue else it.parseObjectByKtx())
                },
                label = $label,
                isRequired = $isRequired
            )
        """.trimIndent()
    }
}
```

### 2. 注册策略

在 `FormFieldStrategyManager.kt` 中添加新策略：

```kotlin
private val strategies = listOf(
    // ... 其他策略
    YourFieldStrategy(),
    // ... 其他策略
    DefaultFieldStrategy() // 默认策略始终放在最后
).sortedBy { it.priority }
```

## 优势

1. **开闭原则**: 添加新类型无需修改现有代码
2. **单一职责**: 每个策略只负责一种字段类型
3. **易于测试**: 每个策略可以独立测试
4. **易于维护**: 逻辑清晰，职责分明
5. **可扩展性**: 轻松添加新的字段类型处理

## 使用示例

```kotlin
// 原来的使用方式不变
fun KSPropertyDeclaration.generateDifferentTypes(): String {
    val type = this.type.resolve()
    return FormFieldStrategyManager.generateCode(this, type)
}
```

策略模式的引入使得代码更加模块化和可维护，同时保持了原有的使用接口不变。
