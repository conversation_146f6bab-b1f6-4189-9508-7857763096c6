package strategy

import com.google.devtools.ksp.symbol.KSPropertyDeclaration
import com.google.devtools.ksp.symbol.KSType

/**
 * 策略注册表 - 最优雅的解决方案
 * 
 * 🎯 解决KMP环境下object延迟初始化的问题
 * 1. 性能优化：只解析一次 KSType
 * 2. 自动发现：策略自动注册
 * 3. 懒加载：只在需要时初始化
 */
object StrategyRegistry {
    
    private val strategies = mutableListOf<FormFieldStrategy>()
    private var initialized = false
    
    /**
     * 策略自注册方法
     */
    fun registerStrategy(strategy: FormFieldStrategy) {
        strategies.add(strategy)
        strategies.sortBy { it.priority }
    }
    
    /**
     * 懒加载初始化所有策略
     */
    private fun ensureInitialized() {
        if (!initialized) {
            // 注册所有策略实例
            registerAllStrategies()
            initialized = true
        }
    }
    
    /**
     * 注册所有策略
     * 这是唯一需要维护的地方，但比原来的when语句简洁很多
     */
    private fun registerAllStrategies() {
        // 按优先级顺序注册
        registerStrategy(strategy.impl.MoneyFieldStrategy)
        registerStrategy(strategy.impl.PercentageFieldStrategy)
        registerStrategy(strategy.impl.PhoneFieldStrategy)
        registerStrategy(strategy.impl.EmailFieldStrategy)
        registerStrategy(strategy.impl.UrlFieldStrategy)
        registerStrategy(strategy.impl.IdCardFieldStrategy)
        registerStrategy(strategy.impl.BankCardFieldStrategy)
        registerStrategy(strategy.impl.IntegerFieldStrategy)
        registerStrategy(strategy.impl.DecimalFieldStrategy)
        registerStrategy(strategy.impl.DateFieldStrategy)
        registerStrategy(strategy.impl.BooleanFieldStrategy)
        registerStrategy(strategy.impl.DefaultFieldStrategy)
    }
    
    /**
     * 生成代码 - 高性能版本
     */
    fun generateCode(property: KSPropertyDeclaration): String {
        ensureInitialized()
        
        val type = property.type.resolve() // 只解析一次！
        
        val strategy = strategies.firstOrNull { it.canHandle(property, type) }
            ?: throw IllegalArgumentException("No strategy found for property: ${property.simpleName.asString()}")
        
        return strategy.generateCode(property, type)
    }
    
    /**
     * 获取已注册策略（调试用）
     */
    fun getRegisteredStrategies(): List<FormFieldStrategy> {
        ensureInitialized()
        return strategies.toList()
    }
}

/**
 * 简化的策略基类
 * 不再依赖自动注册，而是在注册表中统一管理
 */
abstract class RegistryStrategy : BaseFormFieldStrategy()
