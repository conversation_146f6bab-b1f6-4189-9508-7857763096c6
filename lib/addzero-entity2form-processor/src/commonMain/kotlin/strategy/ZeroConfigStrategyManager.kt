package strategy

import com.google.devtools.ksp.symbol.KSPropertyDeclaration
import com.google.devtools.ksp.symbol.KSType

/**
 * 零配置策略管理器
 * 
 * 🎯 终极解决方案：
 * 1. 性能优化：只解析一次 KSType
 * 2. 零配置：添加新策略无需修改任何现有代码
 * 3. 自动注册：策略会在类加载时自动注册
 */
object ZeroConfigStrategyManager {
    
    private val strategies = mutableListOf<FormFieldStrategy>()
    
    /**
     * 策略自注册方法
     */
    fun registerStrategy(strategy: FormFieldStrategy) {
        strategies.add(strategy)
        strategies.sortBy { it.priority }
    }
    
    /**
     * 生成代码 - 性能优化版本
     */
    fun generateCode(property: KSPropertyDeclaration): String {
        val type = property.type.resolve() // 只解析一次！
        
        val strategy = strategies.firstOrNull { it.canHandle(property, type) }
            ?: throw IllegalArgumentException("No strategy found for property: ${property.simpleName.asString()}")
        
        return strategy.generateCode(property, type)
    }
    
    /**
     * 获取已注册策略（调试用）
     */
    fun getRegisteredStrategies(): List<FormFieldStrategy> = strategies.toList()
}

/**
 * 零配置自注册策略基类
 * 
 * 🚀 使用方法：
 * ```kotlin
 * object YourFieldStrategy : ZeroConfigStrategy() {
 *     override val priority: Int = 50
 *     override fun canHandle(property: KSPropertyDeclaration, type: KSType): Boolean = ...
 *     override fun generateCode(property: KSPropertyDeclaration, type: KSType): String = ...
 * }
 * ```
 * 
 * 就这么简单！无需任何其他配置！
 */
abstract class ZeroConfigStrategy : BaseFormFieldStrategy() {
    
    init {
        ZeroConfigStrategyManager.registerStrategy(this)
    }
}
